# Dependency Update MVP

This document describes the MVP (Minimum Viable Product) dependency update system for the Multiverse project. This is a temporary solution designed to keep dependencies up-to-date until a proper Renovate setup is implemented.

## Overview

The MVP dependency update system consists of:

- **Main Script**: `scripts/update-dependencies-mvp.mjs` - Automated dependency updates with safety checks
- **Configuration**: `scripts/dependency-update-config.json` - Settings and critical package definitions
- **Cleanup Script**: `scripts/cleanup-dependency-backups.mjs` - Removes old backup files
- **Package Scripts**: Convenient npm/pnpm scripts for common operations

## Features

### ✅ Safety First
- **Dependency Validation**: Checks for required tools (npx, pnpm, taze) before starting
- **Configuration Validation**: Validates configuration file structure and required sections
- **Input Validation**: Validates command-line arguments with bounds checking
- **Automatic Backups**: Creates timestamped backups of `package.json` before any changes
- **Test Validation**: Runs TypeScript checks, linting, and unit tests after updates
- **Rollback on Failure**: Automatically restores backup if tests fail
- **Critical Package Protection**: Excludes critical packages from major updates
- **Version Pinning**: Pin specific packages to exact versions when newer versions cause issues
- **Version Constraints**: Set maximum allowed versions for problematic packages
- **🆕 Automatic Pinning**: Automatically pins packages that cause test failures and retries

### ✅ Flexibility
- **Dry Run Mode**: Preview changes without making them (`--dry-run`)
- **Update Types**: Choose between minor and major updates
- **Interactive/Non-Interactive**: Supports both manual and CI usage
- **Configurable Limits**: Control maximum number of updates per run

### ✅ Observability
- **Detailed Logging**: Color-coded output showing what's happening
- **Change Tracking**: Clear before/after comparison of updated packages
- **Test Results**: Real-time feedback on validation steps

## Quick Start

### 1. Safe Preview (Recommended First Step)
```bash
pnpm run update-dependencies-safe
```
This runs in dry-run mode to show what would be updated without making changes.

### 2. Minor Updates (Safest)
```bash
pnpm run update-dependencies-minor
```
Updates to latest minor/patch versions only.

### 3. Include Major Updates (Use with Caution)
```bash
pnpm run update-dependencies-mvp --include-major
```
Includes major version updates (may introduce breaking changes).

## Available Commands

| Command | Description |
|---------|-------------|
| `pnpm run update-dependencies-safe` | Dry run - preview changes only |
| `pnpm run update-dependencies-minor` | Update minor/patch versions |
| `pnpm run update-dependencies-mvp` | Full script with all options |
| `pnpm run cleanup-dependency-backups` | Remove old backup files |

## Command Line Options

### Main Script Options
```bash
node scripts/update-dependencies-mvp.mjs [options]
```

| Option | Description | Default |
|--------|-------------|---------|
| `--dry-run` | Preview changes without making them | false |
| `--skip-tests` | Skip running tests after updates | false |
| `--non-interactive` | Run without prompts (for CI) | false |
| `--include-major` | Include major version updates | false |
| `--max-updates=N` | Maximum packages to update (1-100) | 20 |
| `--no-auto-pin` | Disable automatic pinning | false |
| `--help, -h` | Show help message | - |

### Cleanup Script Options
```bash
node scripts/cleanup-dependency-backups.mjs [options]
```

| Option | Description | Default |
|--------|-------------|---------|
| `--days=N` | Days to retain backups (1-365) | 7 |
| `--dry-run` | Preview what would be deleted | false |
| `--help, -h` | Show help message | - |

## Configuration

The script uses `scripts/dependency-update-config.json` for configuration:

### Critical Packages
Packages that are excluded from major updates due to their importance:
- React and React DOM
- TypeScript
- Vite and Remix
- MobX
- Styled Components

### Version Pinning & Constraints

#### Pinned Versions
When a specific package version causes breaking changes, you can pin it to a known working version. The system supports both simple string versions and detailed objects with explanations:

```json
{
  "pinnedVersions": {
    "packages": {
      "problematic-package": "1.2.3",
      "another-package": {
        "version": "^2.1.0",
        "reason": "Manually pinned - newer versions break our custom configuration"
      },
      "auto-pinned-package": {
        "version": "3.4.5",
        "reason": "Auto-pinned after test failure - major version update likely to cause breaking changes (3.4.5 → 4.0.0)"
      }
    }
  }
}
```

**Supported formats:**
- **String format**: `"package-name": "1.2.3"` (legacy format, still supported)
- **Object format**: `"package-name": { "version": "1.2.3", "reason": "explanation" }` (recommended)

**Use cases:**
- A new version introduces breaking changes
- Waiting for a bug fix in a newer version
- Compatibility issues with other dependencies
- Automatic pinning after test failures (includes detailed explanations)

#### Version Constraints
Set maximum allowed versions for packages that are known to have issues:

```json
{
  "versionConstraints": {
    "packages": {
      "example-package": "<3.0.0",
      "another-example": ">=1.0.0 <2.5.0"
    }
  }
}
```

**Use cases:**
- Prevent automatic updates to known problematic versions
- Maintain compatibility with specific version ranges
- Gradual migration strategies

#### 🆕 Automatic Pinning
The script can automatically pin packages that cause test failures:

```json
{
  "autoPinning": {
    "enabled": true,
    "maxRetries": 2,
    "logAutoPins": true,
    "pinToLastWorkingVersion": true
  }
}
```

**How it works:**
1. **Update attempt** → Script updates dependencies
2. **Tests fail** → Script detects test failure
3. **Auto-pin** → Script automatically pins problematic packages to last working versions
4. **Retry** → Script runs update again with pins in place
5. **Success** → Only safe updates applied, problematic ones pinned

**Benefits:**
- ✅ **Fully automated** - no manual intervention needed
- ✅ **Zero-downtime updates** - always ends in a working state
- ✅ **Detailed audit trail** - config file shows what was auto-pinned with explanations
- ✅ **Smart reasoning** - explains why each package was pinned (major update, testing tool, etc.)

**Control:**
- Use `--no-auto-pin` flag to disable for specific runs
- Set `"enabled": false` in config to disable globally



### Test Commands
The script runs these validation steps after updates:
1. **Type Check** (`pnpm run typecheck`) - Required
2. **Lint** (`pnpm run lint`) - Required
3. **Unit Tests** (`pnpm run test --run`) - Required
4. **Build Check** (`pnpm run tokens`) - Optional

## Workflow Examples

### Daily Development Workflow
```bash
# 1. Check what's available
pnpm run update-dependencies-safe

# 2. Update minor versions if changes look good
pnpm run update-dependencies-minor

# 3. Commit changes if tests pass
git add package.json pnpm-lock.yaml
git commit -m "chore: update dependencies"
```

### Weekly Maintenance
```bash
# 1. Clean up old backups
pnpm run cleanup-dependency-backups

# 2. Check for major updates
pnpm run update-dependencies-mvp --include-major --dry-run

# 3. Apply major updates selectively (manual review recommended)
```

### CI/Automated Usage
```bash
# Non-interactive mode for automation
node scripts/update-dependencies-mvp.mjs --non-interactive --skip-tests
```

### 🆕 Automatic Handling of Breaking Changes
With auto-pinning enabled (default), the script handles breaking changes automatically:

```bash
# 1. Run update as normal
pnpm run update-dependencies-minor

# 2. Script detects test failure and auto-pins problematic packages
# ⚠️  Tests failed on attempt 1, trying auto-pinning...
# 📌 Auto-pinning problematic-package to last working version: 1.2.3
# ✅ Auto-pinned 1 package(s) in configuration

# 3. Script automatically retries with pins in place
# ✅ Success achieved after 2 attempt(s) with auto-pinning

# 4. Check what was auto-pinned in the config file
# scripts/dependency-update-config.json now contains the pin
```

### Manual Handling of Breaking Changes
If you prefer manual control, disable auto-pinning:

```bash
# Disable auto-pinning for this run
pnpm run update-dependencies-mvp --no-auto-pin

# Or disable globally in config:
{
  "autoPinning": {
    "enabled": false
  }
}

# Then manually pin problematic packages:
{
  "pinnedVersions": {
    "packages": {
      "problematic-package": {
        "version": "1.2.3",
        "reason": "Manually pinned - breaking changes in newer versions"
      }
    }
  }
}
```

## Safety Mechanisms

### 1. Backup System
- Automatic backup creation before any changes
- Timestamped filenames for easy identification
- Automatic restoration on test failures
- Configurable retention period (default: 7 days)

### 2. Test Validation
- TypeScript compilation check
- ESLint validation
- Unit test execution
- Optional build verification

### 3. Critical Package Protection
- Predefined list of packages excluded from major updates
- Configurable exclusion rules
- Special handling for framework dependencies

### 4. Input Validation
- Command-line argument validation with bounds checking
- Configuration file structure validation
- Dependency availability checks before execution

### 5. Rollback Capability
- Automatic rollback on test failures
- Manual rollback using backup files
- Clear error messages and recovery instructions

## Limitations & Considerations

### Current Limitations
- **No Dependency Analysis**: Doesn't analyze dependency relationships
- **No Security Scanning**: Doesn't check for security vulnerabilities
- **No Route Impact Analysis**: Doesn't predict which routes might be affected by changes
- **No Change Impact Analysis**: Doesn't predict breaking changes
- **Single Package.json**: Only handles root package.json (not workspace packages)

### When to Use Manual Updates
- Major framework updates (React, Remix, etc.)
- Dependencies with known breaking changes
- Packages requiring configuration changes
- Critical security updates requiring immediate attention

### Migration to Renovate
This MVP system should be replaced with Renovate when:
- Team has bandwidth to configure Renovate properly
- Need for more sophisticated dependency analysis
- Want automated PR creation and management
- Require integration with security scanning tools

## Troubleshooting

### Common Issues

#### Tests Fail After Update
```bash
# Script automatically restores backup, but you can also manually restore:
cp package.json.backup.TIMESTAMP package.json
pnpm install
```

#### Too Many Updates at Once
```bash
# Limit the number of updates
pnpm run update-dependencies-mvp --max-updates=5
```

#### Script Fails to Run
```bash
# Ensure you're in the project root
cd /path/to/multiverse

# Check that taze is available
npx taze --version

# Verify environment
ls package.json taze.config.js
```

### Recovery Procedures

#### Restore from Backup
```bash
# List available backups
ls package.json.backup.*

# Restore specific backup
cp package.json.backup.2024-07-01T10-30-00-000Z package.json
pnpm install
```

#### Clean Slate Recovery
```bash
# Reset to git state
git checkout package.json pnpm-lock.yaml
pnpm install
```

## Best Practices

### Before Running Updates
1. Ensure working directory is clean (`git status`)
2. Run tests to establish baseline (`pnpm test`)
3. Check for any known issues with current dependencies

### After Running Updates
1. Review the changes made (`git diff package.json`)
2. Run full test suite if available
3. Test critical application functionality
4. Commit changes with descriptive message

### Regular Maintenance
1. Run cleanup script weekly to remove old backups
2. Review excluded packages periodically
3. Monitor for security advisories
4. Plan migration to Renovate

## Support

For issues or questions about the dependency update MVP:
1. Check this documentation first
2. Review the script output for error messages
3. Check backup files for recovery options
4. Reach out to #program-constellation Slack channel

---

**Note**: This is a temporary MVP solution. The long-term plan is to migrate to Renovate for more sophisticated dependency management.
