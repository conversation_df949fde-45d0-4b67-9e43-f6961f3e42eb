import {
    type RBACSubject,
    sharedCurrentUserController,
} from '@globals/current-user';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { makeAutoObservable } from '@globals/mobx';
import { sharedFeatureFlagsController } from './controllers/feature-flags-controller';

class FeatureAccessModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isCustomWorkflowsEnabled(): boolean {
        return (
            sharedEntitlementFlagController.isCustomWorkflowsEnabled &&
            sharedFeatureFlagsController.isReleaseCustomWorkflowsEnabled
        );
    }

    get isMultipleWorkspacesEnabled(): boolean {
        return sharedEntitlementFlagController.isMultipleWorkspacesEnabled;
    }

    get isReleaseVrmPublicTrustCenterViewCollection(): boolean {
        return sharedFeatureFlagsController.isReleaseVrmPublicTrustCenterViewCollection;
    }

    get isVendorRiskManagementProEnabled(): boolean {
        return sharedEntitlementFlagController.isVendorRiskManagementProEnabled;
    }

    get isCustomFrameworksEnabled(): boolean {
        return sharedEntitlementFlagController.isCustomFrameworksEnabled;
    }

    get isDownloadControlEnabled(): boolean {
        return sharedEntitlementFlagController.isDownloadControlEnabled;
    }

    get isUserAccessReviewEnabled(): boolean {
        return sharedEntitlementFlagController.isUserAccessReviewEnabled;
    }

    get isNavReadEnabled(): boolean {
        // TODO: double check permissions with RBAC.

        return true;
    }

    get isQuickStartReadEnabled(): boolean {
        return this.isNavReadEnabled;
    }

    get isDashboardDomainReadEnabled(): boolean {
        return this.isNavReadEnabled;
    }

    get isMapControlsTestsEnabled(): boolean {
        return sharedEntitlementFlagController.isMapControlsTestsEnabled;
    }

    get isTasksDomainReadEnabled(): boolean {
        const subjectsEnablingTasksDomain: RBACSubject[] = [
            'ControlTask',
            'CreateTask',
            'DeleteTask',
            'EvidenceTask',
            'GeneralTask',
            'PolicyTask',
            'RiskTask',
            'UpdateTask',
            'VendorTask',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingTasksDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get hasLimitedAccess(): boolean {
        const { isAuditorInReadOnlyMode, isUserInReadOnlyMode } =
            sharedCurrentUserController;

        const hasManage = sharedCurrentUserController.hasUserPermission(
            'AccessReview',
            'MANAGE',
        );

        return !(isAuditorInReadOnlyMode || isUserInReadOnlyMode || hasManage);
    }

    get isComplianceDomainReadEnabled(): boolean {
        const subjectsEnablingComplianceDomain: RBACSubject[] = [
            'Control',
            'ControlTask',
            'ControlTemplate',
            'ControlTicket',
            'Framework',
            'FrameworkMappingReset',
            'EvidenceTask',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingComplianceDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isRiskDomainReadEnabled(): boolean {
        const subjectsEnablingRiskDomain: RBACSubject[] = [
            'Risk',
            'RiskAssessment',
            'RiskManagementTicket',
            'RiskTask',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingRiskDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isVendorsDomainReadEnabled(): boolean {
        const subjectsEnablingVendorsDomain: RBACSubject[] = [
            'Vendor',
            'VendorTask',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingVendorsDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isVendorInsightsReadEnabled(): boolean {
        return (
            this.isNavReadEnabled &&
            sharedCurrentUserController.hasUserPermission(
                'ViewAllRisks',
                'READ',
            ) &&
            sharedEntitlementFlagController.isVendorRiskManagementProEnabled
        );
    }

    get isAccessReviewReadEnabled(): boolean {
        return (
            this.isNavReadEnabled &&
            sharedCurrentUserController.hasUserPermission(
                'AccessReview',
                'READ',
            ) &&
            sharedEntitlementFlagController.isUserAccessReviewEnabled
        );
    }

    get isAccessReviewManageEnabled(): boolean {
        return (
            this.isNavReadEnabled &&
            sharedCurrentUserController.hasUserPermission(
                'AccessReview',
                'MANAGE',
            ) &&
            sharedEntitlementFlagController.isUserAccessReviewEnabled
        );
    }

    get isGovernanceDomainReadEnabled(): boolean {
        const subjectsEnablingGovernanceDomain: RBACSubject[] = [
            'AccessReview',
            'AssociateRequirements',
            'CacConfiguration',
            'CacMonitoring',
            'Monitors',
            'SecurityQuestionnaire',
            'TicketManagement',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingGovernanceDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isTrustDomainReadEnabled(): boolean {
        const subjectsEnablingTrustDomain: RBACSubject[] = [
            'TrustCenter',
            'TrustCenterRequest',
        ];

        return (
            this.isNavReadEnabled &&
            sharedEntitlementFlagController.isTrustCenterEnabled &&
            subjectsEnablingTrustDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isLibraryDomainReadEnabled(): boolean {
        const subjectsEnablingLibraryDomain: RBACSubject[] = [
            'Control',
            'ControlTask',
            'ControlTemplate',
            'ControlTicket',
            'Framework',
            'FrameworkMappingReset',
            'EvidenceTask',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingLibraryDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isConnectionsDomainReadEnabled(): boolean {
        const subjectsEnablingConnectionsDomain: RBACSubject[] = [
            'Connection',
            'CustomConnections',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingConnectionsDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isEventsDomainReadEnabled(): boolean {
        const subjectsEnablingEventsDomain: RBACSubject[] = [
            'AccessReview',
            'AssociateRequirements',
            'CacConfiguration',
            'CacMonitoring',
            'Monitors',
            'SecurityQuestionnaire',
            'TicketManagement',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingEventsDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isSettingsDomainReadEnabled(): boolean {
        const subjectsEnablingSettingsDomain: RBACSubject[] = [
            'Company',
            'UserSettings',
        ];

        return (
            this.isNavReadEnabled &&
            subjectsEnablingSettingsDomain.some((subject) => {
                return sharedCurrentUserController.hasUserPermission(
                    subject,
                    'READ',
                );
            })
        );
    }

    get isShowSyncronCustomReports(): boolean {
        return sharedFeatureFlagsController.isShowSyncronCustomReports;
    }

    get isEarlyAccessEnabled(): boolean {
        return sharedFeatureFlagsController.isEarlyAccessEnabled;
    }

    get hasRiskReadPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission('Risk', 'READ');
    }

    get isRiskManagerWithRestrictedView(): boolean {
        return sharedCurrentUserController.isRiskManagerWithRestrictedView;
    }

    get hasGeneralTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'GeneralTask',
            'MANAGE',
        );
    }

    get hasCreateTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'CreateTask',
            'MANAGE',
        );
    }

    get hasEvidenceTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'EvidenceTask',
            'MANAGE',
        );
    }

    get hasPolicyTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'PolicyTask',
            'MANAGE',
        );
    }

    get hasVendorTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'VendorTask',
            'MANAGE',
        );
    }

    get hasControlTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'ControlTask',
            'MANAGE',
        );
    }

    get hasRiskTaskPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'RiskTask',
            'MANAGE',
        );
    }

    get hasPolicyManagePermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Policy',
            'MANAGE',
        );
    }

    get hasReadEvidenceLibraryPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission('Report', 'READ');
    }

    get hasWriteEvidenceLibraryPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Report',
            'MANAGE',
        );
    }

    get hasVendorManagePermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Vendor',
            'MANAGE',
        );
    }

    get hasCompareToDefaultMonitoringPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'ControlTemplate',
            'MANAGE',
        );
    }

    get hasReadControlPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission('Control', 'READ');
    }

    get hasWriteControlPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Control',
            'MANAGE',
        );
    }

    get hasWriteFrameworkPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Framework',
            'MANAGE',
        );
    }

    get hasWriteFrameworkMappingResetPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'FrameworkMappingReset',
            'MANAGE',
        );
    }

    get hasAssociateRequirementsPermission(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'AssociateRequirements',
            'MANAGE',
        );
    }

    get isVendorEditable(): boolean {
        return (
            this.hasVendorManagePermission &&
            !sharedCurrentUserController.isRiskManagerWithRestrictedView
        );
    }

    get isReleaseBulkImportRiskEnabled(): boolean {
        return sharedFeatureFlagsController.isReleaseBulkImportRiskEnabled;
    }
}

export const sharedFeatureAccessModel = new FeatureAccessModel();
