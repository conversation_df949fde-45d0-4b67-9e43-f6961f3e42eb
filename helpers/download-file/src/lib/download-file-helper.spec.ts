import { beforeEach, describe, expect, test, vi } from 'vitest';
/* eslint-disable @typescript-eslint/unbound-method -- overzealous because i'm overwriting them */
import {
    codeToJSONString,
    downloadBase64AsFile,
    downloadBlob,
    isRawEvidenceDataTooLarge,
    sanitizeFileName,
} from './download-file-helper';

describe('downloadBlob', () => {
    beforeEach(() => {
        // Set up URL object with required methods
        Object.defineProperty(window, 'URL', {
            value: {
                createObjectURL: vi.fn(() => 'mock-blob-url'),
                revokeObjectURL: vi.fn(),
            },
            writable: true,
        });

        // Mock document.createElement and click
        const mockAnchor = {
            href: '',
            download: '',
            click: vi.fn(),
        };

        vi.spyOn(document, 'createElement').mockImplementation(
            () => mockAnchor as unknown as HTMLElement,
        );
    });

    test('creates and clicks an anchor with correct blob URL and filename', () => {
        const testBlob = new Blob(['test content'], { type: 'text/plain' });
        const fileName = 'test-file.txt';

        downloadBlob(testBlob, fileName);

        // Verify URL creation and cleanup
        expect(window.URL.createObjectURL).toHaveBeenCalledWith(testBlob);
        expect(window.URL.revokeObjectURL).toHaveBeenCalledWith(
            'mock-blob-url',
        );

        // Verify anchor element creation and properties
        expect(document.createElement).toHaveBeenCalledWith('a');
        const anchor = document.createElement('a') as unknown as {
            href: string;
            download: string;
            click: () => void;
        };

        expect(anchor.href).toBe('mock-blob-url');
        expect(anchor.download).toBe(fileName);
        expect(anchor.click).toHaveBeenCalled();
    });
});

describe('downloadBase64AsFile', () => {
    let mockCreateObjectURL: ReturnType<typeof vi.fn>;
    let mockRevokeObjectURL: ReturnType<typeof vi.fn>;
    let mockAnchorClick: ReturnType<typeof vi.fn>;
    let mockAnchor: HTMLAnchorElement;

    beforeEach(() => {
        mockCreateObjectURL = vi.fn(() => 'mock-blob-url');
        mockRevokeObjectURL = vi.fn();

        Object.defineProperty(window, 'URL', {
            value: {
                createObjectURL: mockCreateObjectURL,
                revokeObjectURL: mockRevokeObjectURL,
            },
            writable: true,
        });

        mockAnchorClick = vi.fn();
        mockAnchor = {
            href: '',
            download: '',
            click: mockAnchorClick,
        } as unknown as HTMLAnchorElement;

        vi.spyOn(document, 'createElement').mockImplementation(
            () => mockAnchor,
        );

        vi.spyOn(global, 'atob').mockImplementation((str: string) => {
            return str;
        });
    });

    test('converts base64 to blob and triggers download', () => {
        const base64Data = 'dGVzdCBjb250ZW50';
        const fileName = 'test-file.pdf';
        const mimeType = 'application/pdf';

        downloadBase64AsFile(base64Data, fileName, mimeType);

        expect(global.atob).toHaveBeenCalledWith(base64Data);

        expect(mockCreateObjectURL).toHaveBeenCalled();
        expect(mockRevokeObjectURL).toHaveBeenCalledWith('mock-blob-url');

        expect(document.createElement).toHaveBeenCalledWith('a');
        expect(mockAnchor.href).toBe('mock-blob-url');
        expect(mockAnchor.download).toBe(fileName);
        expect(mockAnchorClick).toHaveBeenCalled();
    });

    test('handles different MIME types correctly', () => {
        const base64Data = 'dGVzdA==';
        const fileName = 'test.csv';
        const mimeType = 'text/csv';

        downloadBase64AsFile(base64Data, fileName, mimeType);

        expect(mockCreateObjectURL).toHaveBeenCalledWith(
            expect.objectContaining({
                type: mimeType,
            }),
        );
    });

    test('handles empty base64 data', () => {
        const base64Data = '';
        const fileName = 'empty-file.txt';
        const mimeType = 'text/plain';

        expect(() => {
            downloadBase64AsFile(base64Data, fileName, mimeType);
        }).not.toThrow();

        expect(global.atob).toHaveBeenCalledWith(base64Data);
    });
});

describe('sanitizeFileName', () => {
    test('replaces single spaces with hyphens', () => {
        expect(sanitizeFileName('my file name.txt')).toBe('my-file-name.txt');
    });

    test('replaces multiple consecutive spaces with single hyphen', () => {
        expect(sanitizeFileName('my   file    name.txt')).toBe(
            'my-file-name.txt',
        );
    });

    test('handles empty string', () => {
        expect(sanitizeFileName('')).toBe('');
    });

    test('handles undefined input', () => {
        expect(sanitizeFileName(undefined)).toBe('');
    });

    test('preserves other special characters', () => {
        expect(sanitizeFileName('my.file_name!@#.txt')).toBe(
            'my.file_name!@#.txt',
        );
    });
});

describe('codeToJSONString', () => {
    test('converts object to indented JSON string by default', () => {
        const testObj = { name: 'test', value: 123 };
        const expected = JSON.stringify(testObj, null, '\t');

        expect(codeToJSONString(testObj)).toBe(expected);
    });

    test('converts object to non-indented JSON string when indented is false', () => {
        const testObj = { name: 'test', value: 123 };
        const expected = JSON.stringify(testObj);

        expect(codeToJSONString(testObj, false)).toBe(expected);
    });

    test('handles null values', () => {
        const testObj = { name: 'test', value: null };
        const expected = JSON.stringify(testObj, null, '\t');

        expect(codeToJSONString(testObj)).toBe(expected);
    });

    test('handles nested objects', () => {
        const testObj = {
            name: 'test',
            nested: {
                value: 123,
                array: [1, 2, 3],
            },
        };
        const expected = JSON.stringify(testObj, null, '\t');

        expect(codeToJSONString(testObj)).toBe(expected);
    });

    test('returns empty object string for nil inputs', () => {
        // @ts-expect-error Testing null input
        expect(codeToJSONString(null)).toBe('{}');
        // @ts-expect-error Testing undefined input
        expect(codeToJSONString(undefined)).toBe('{}');
    });
});

describe('isRawEvidenceDataTooLarge', () => {
    test('returns false for null input', () => {
        expect(isRawEvidenceDataTooLarge(null)).toBeFalsy();
    });

    test('returns false for undefined input', () => {
        expect(isRawEvidenceDataTooLarge(undefined)).toBeFalsy();
    });

    test('returns false for empty string', () => {
        expect(isRawEvidenceDataTooLarge('')).toBeFalsy();
    });

    test('returns false for small JSON string', () => {
        const smallJson = JSON.stringify({ name: 'test', value: 123 });

        expect(isRawEvidenceDataTooLarge(smallJson)).toBeFalsy();
    });

    test('respects custom maxSizeMB parameter', () => {
        // Create a medium-sized object
        const mediumObject = {};

        for (let i = 0; i < 100; i = i + 1) {
            (mediumObject as Record<string, string>)[`key${i}`] =
                `value${i}`.repeat(10);
        }
        const mediumJson = JSON.stringify(mediumObject);

        // Should be false with a higher threshold
        expect(isRawEvidenceDataTooLarge(mediumJson, 1)).toBeFalsy();

        // Should be true with a very low threshold
        expect(isRawEvidenceDataTooLarge(mediumJson, 0.0001)).toBeTruthy();
    });
});
