import { isNil } from 'lodash-es';

/**
 * Downloads a Blob as a file.
 *
 * This function creates a temporary URL for the Blob,
 * creates an invisible anchor element, sets its href to the Blob URL,
 * programmatically clicks it to trigger the download, and then
 * revokes the Blob URL to free up resources.
 *
 * @param blob - The Blob to be downloaded.
 * @param fileName - The name to give the downloaded file.
 */
export const downloadBlob = (blob: Blob, fileName: string): void => {
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');

    a.href = url;
    a.download = fileName;
    a.click();

    window.URL.revokeObjectURL(url);
};

export const downloadFileFromSignedUrl = (signedUrl: string): void => {
    window.open(signedUrl, '_blank', 'noopener, noreferrer');
};

/**
 * Converts base64 encoded data to a Blob and triggers download.
 *
 * This function takes base64 encoded file data, converts it to a binary Blob,
 * and triggers a download using the provided filename and MIME type.
 * Useful for downloading files received as base64 strings from APIs.
 *
 * @param base64Data - The base64 encoded file data (without data URL prefix).
 * @param fileName - The name to give the downloaded file.
 * @param mimeType - The MIME type of the file (e.g., 'application/pdf', 'text/csv').
 */
export const downloadBase64AsFile = (
    base64Data: string,
    fileName: string,
    mimeType: string,
): void => {
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i = i + 1) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    const blob = new Blob([bytes], {
        type: mimeType,
    });

    downloadBlob(blob, fileName);
};

/**
 * Sanitizes a filename by replacing spaces with hyphens.
 *
 * This function takes a filename and replaces all spaces with hyphens.
 * It is useful for creating valid filenames from user-provided data.
 *
 * @param filename - The filename to sanitize.
 * @returns The sanitized filename.
 */
export const sanitizeFileName = (filename = ''): string => {
    const spaces = /\s+/g;

    return filename.replaceAll(spaces, '-');
};

/**
 * Converts a code object to a JSON string.
 *
 * This function converts a code object to a JSON string.
 * It is useful for creating a JSON string from a code object.
 *
 * @param code - The code object to convert.
 * @param indented - Whether to indent the JSON string.
 * @returns The JSON string.
 */
export const codeToJSONString = (code: object, indented = true): string => {
    // Use noop replacer to handle larger JSON objects (up to max event size 16MB)
    if (isNil(code)) {
        return '{}';
    }

    return JSON.stringify(
        code,
        (_key, value) => value as string | number | boolean | object | null,
        indented ? '\t' : undefined,
    );
};

/**
 * Checks if the raw evidence data is too large to display.
 *
 * This function calculates the size of the JSON string in megabytes
 * and compares it to a maximum threshold.
 *
 * @param json - The JSON string to check.
 * @param maxSizeMB - The maximum size in megabytes (default: 2).
 * @returns True if the data is too large, false otherwise.
 */
export const isRawEvidenceDataTooLarge = (
    json: string | null | undefined,
    maxSizeMB = 2,
): boolean => {
    if (isNil(json)) {
        return false;
    }

    const VALUE_SIZE = 1024;
    // Use the actual JSON string length rather than parsing it again
    const codeLength = new TextEncoder().encode(json).length;

    return codeLength / VALUE_SIZE / VALUE_SIZE > maxSizeMB;
};
