import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedAuditHubAuditorRefreshPackageController } from '@controllers/audit-hub-auditor-refresh-package';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { modalController } from '@controllers/modal';
import { AuditHubAuditorClientAuditResourcesModalDetailsView } from '@views/auditor-client-audit-details';

export const openHandleRequestModal = (): void => {
    const handleClose = () => {
        modalController.closeModal('audit-hub-client-audit-request-modal');
    };

    modalController.openModal({
        id: 'audit-hub-client-audit-request-modal',
        content: () => (
            <AuditHubAuditorClientAuditResourcesModalDetailsView
                data-id=""
                isSubmitting={
                    sharedAuditHubAuditorRefreshPackageController.isAuditorRefreshControlEvidencePackageLoading
                }
                handleOnSubmit={() => {
                    sharedAuditHubAuditorRefreshPackageController.refreshControlEvidencePackage(
                        sharedCustomerRequestsController.frameworkId,
                        sharedAuditHubController.auditByIdData?.framework
                            .productId as number,
                    );
                }}
                onClose={handleClose}
            />
        ),
        centered: true,
        size: 'md',
    });
};
