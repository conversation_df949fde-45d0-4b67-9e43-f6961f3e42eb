import { beforeAll, describe, expect, test, vi } from 'vitest';
import { i18n } from '@globals/i18n';
import {
    areSampleDatesWithinAuditorFrameworkPeriod,
    getIsArchiveExpired,
} from './auditor-client-audit-model-helpers';

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));
describe('getIsArchiveExpired', () => {
    test('should return true when companyArchiveUpdatedAt is null', () => {
        expect(getIsArchiveExpired(null as unknown as Date)).toBeTruthy();
    });

    test('should return true when companyArchiveUpdatedAt is undefined', () => {
        expect(getIsArchiveExpired(undefined as unknown as Date)).toBeTruthy();
    });

    test('should return true when current date is after expiration date', () => {
        // Mock current date to a fixed value
        const mockDate = new Date('2023-01-02T12:00:00Z');

        vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

        // Create a date 25 hours in the past (beyond 24 hour expiration)
        const pastDate = new Date('2023-01-01T10:00:00Z');

        expect(getIsArchiveExpired(pastDate)).toBeTruthy();

        vi.restoreAllMocks();
    });

    test('should return false when current date is in the future', () => {
        const twoDaysLater = new Date();

        twoDaysLater.setDate(twoDaysLater.getDate() + 2);

        expect(getIsArchiveExpired(twoDaysLater)).toBeFalsy();

        vi.restoreAllMocks();
    });

    test('should return true when current date equals expiration date', () => {
        // Mock current date to exactly match expiration
        const mockDate = new Date('2023-01-01T12:00:00Z');

        vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

        // Create a date that will expire exactly at the mock current time
        const expirationDate = new Date('2023-01-01T12:00:00Z');

        expirationDate.setHours(expirationDate.getHours() - 24);

        expect(getIsArchiveExpired(expirationDate)).toBeTruthy();

        vi.restoreAllMocks();
    });
});

describe('areSampleDatesWithinAuditorFrameworkPeriod', () => {
    beforeAll(() => {
        i18n.load('en-US', {});
        i18n.activate('en-US');
    });

    test('should return true when all sample dates are within the framework period', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-03-15', '2023-06-20', '2023-09-10'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should return true when sample dates are exactly on the boundary dates', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-01-01', '2023-12-31'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should return false when any sample date is before the start date', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2022-12-31', '2023-06-20'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should return false when any sample date is after the end date', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-06-20', '2024-01-01'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should return true when sample dates array is empty', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates: string[] = [];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should return false when sampleDates is not an array', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = null as unknown as string[];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should return false when start date is invalid', () => {
        const startDate = 'invalid-date';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-06-20'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should return false when end date is invalid', () => {
        const startDate = '2023-01-01';
        const endDate = 'invalid-date';
        const sampleDates = ['2023-06-20'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should return false when any sample date is invalid', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-06-20', 'invalid-date'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should handle ISO date strings correctly', () => {
        const startDate = '2023-01-01T00:00:00Z';
        const endDate = '2023-12-31T23:59:59Z';
        const sampleDates = ['2023-06-15T12:30:00Z', '2023-09-20T08:45:00Z'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should handle mixed date formats', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31T23:59:59Z';
        const sampleDates = ['2023-06-15', '2023-09-20T08:45:00Z'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should return false when start date is after end date', () => {
        const startDate = '2023-12-31';
        const endDate = '2024-01-01';
        const sampleDates = ['2025-06-20'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should handle single sample date correctly', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        const sampleDates = ['2023-06-15'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should handle edge case with same start and end date', () => {
        const startDate = '2023-06-15';
        const endDate = '2023-06-15';
        const sampleDates = ['2023-06-15'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });

    test('should return false when sample date is outside same-day period', () => {
        const startDate = '2023-06-15';
        const endDate = '2023-06-15';
        const sampleDates = ['2023-06-16'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should handle mixed valid and invalid dates correctly', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        // This test exposes the nested loop bug - if one date is valid and within range,
        // but another is invalid, the function should return false
        const sampleDates = ['2023-06-15', 'invalid-date'];

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeFalsy();
    });

    test('should handle performance with many dates', () => {
        const startDate = '2023-01-01';
        const endDate = '2023-12-31';
        // Generate many dates within the range
        const sampleDates = Array.from({ length: 100 }, (_, i) => {
            const date = new Date('2023-01-01');

            date.setDate(date.getDate() + i * 3); // Every 3 days

            return date.toISOString().split('T')[0];
        }).filter((date) => new Date(date) <= new Date(endDate));

        expect(
            areSampleDatesWithinAuditorFrameworkPeriod(
                startDate,
                endDate,
                sampleDates,
            ),
        ).toBeTruthy();
    });
});
