import { isNil } from 'lodash-es';

const LINK_EXPIRATION_TIME_IN_HOURS = 24;

export const getIsArchiveExpired = (companyArchiveUpdatedAt: Date): boolean => {
    return isNil(companyArchiveUpdatedAt)
        ? true
        : (() => {
              const currentMomentDate = new Date();
              const updatedAtMomentDate = new Date(companyArchiveUpdatedAt);

              const expirationDate = new Date(
                  updatedAtMomentDate.getTime() +
                      LINK_EXPIRATION_TIME_IN_HOURS * 60 * 60 * 1000,
              );

              return currentMomentDate >= expirationDate;
          })();
};

export function areSampleDatesWithinAuditorFrameworkPeriod(
    startDate: string,
    endDate: string,
    sampleDates: string[],
): boolean {
    if (!Array.isArray(sampleDates)) {
        return false;
    }

    const auditorFrameworkStartDate = new Date(startDate);
    const auditorFrameworkEndDate = new Date(endDate);

    if (
        Number.isNaN(auditorFrameworkStartDate.getTime()) ||
        Number.isNaN(auditorFrameworkEndDate.getTime())
    ) {
        return false;
    }

    const start = auditorFrameworkStartDate.getTime();
    const end = auditorFrameworkEndDate.getTime();

    return sampleDates.every((date) => {
        const sampleDate = new Date(date);

        if (Number.isNaN(sampleDate.getTime())) {
            return false;
        }

        const sampleGetTime = sampleDate.getTime();

        return sampleGetTime >= start && sampleGetTime <= end;
    });
}
