import { isError } from 'lodash-es';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { modalController } from '@controllers/modal';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer, when } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { SetAuditSamples } from '../../../../views/audit-hub-wizard-sample/src/lib/steps/set-audit-samples';

const MODAL_ID = 'change-sample-evidence-modal';

const handleClose = (): void => {
    modalController.closeModal(MODAL_ID);
};

/**
 * Modal component for changing sample evidence in an audit.
 * Handles form submission and data loading states.
 */
const ChangeSampleEvidenceModal = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const { auditByIdIsLoading } = sharedAuditHubController;
    const { auditorPersonnelIsLoading } = sharedAuditorController;

    const isLoading = auditByIdIsLoading || auditorPersonnelIsLoading;

    const handleSave = (): void => {
        triggerSubmit()
            .then((isFormValid) => {
                if (!isFormValid) {
                    return;
                }

                sharedAuditHubAuditController
                    ._generateControlEvidence()
                    .catch((error) => {
                        logger.error({
                            message: 'Error generating control evidence',
                            errorObject: {
                                message: isError(error)
                                    ? error.message
                                    : String(error),
                                statusCode: '500',
                            },
                        });
                    });

                when(
                    () =>
                        !sharedAuditHubAuditController
                            .generateControlEvidencePackage.isPending,
                    () => {
                        if (
                            !sharedAuditHubAuditController
                                .generateControlEvidencePackage.mutation?.state
                                ?.isError
                        ) {
                            handleClose();
                        }
                    },
                );
            })
            .catch((error: unknown) => {
                logger.error({
                    message: 'Error saving sample evidence changes',
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: '500',
                    },
                });
                // Don't close modal on error so user can retry
            });
    };

    return (
        <>
            <Modal.Header
                title={t`Change sample evidence`}
                closeButtonAriaLabel={t`Close modal`}
                data-id="change-sample-evidence-modal-header"
                onClose={handleClose}
            />
            <Modal.Body>
                {isLoading ? (
                    <Stack justify="center" align="center" height="200px">
                        <Loader
                            label={t`Loading audit data...`}
                            data-id="change-sample-evidence-modal-loader"
                        />
                    </Stack>
                ) : (
                    <SetAuditSamples formRef={formRef} />
                )}
            </Modal.Body>
            <Modal.Footer
                data-id="change-sample-evidence-modal-footer"
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Save changes`,
                        level: 'primary',
                        onClick: handleSave,
                        cosmosUseWithCaution_isDisabled: isLoading,
                    },
                ]}
            />
        </>
    );
});

/**
 * Opens the change sample evidence modal for the specified audit.
 * Handles data loading and modal initialization.
 */
export const openChangeSampleEvidenceModal = (auditId: string): void => {
    sharedAuditHubController.loadAuditById(auditId);

    when(
        () => Boolean(sharedAuditHubController.auditByIdData?.framework),
        () => {
            const { auditByIdData } = sharedAuditHubController;
            const { entryId } = sharedCurrentUserController;

            if (auditByIdData?.framework) {
                sharedAuditorController.loadAuditorPersonnel({
                    entryId,
                    auditorFrameworkId: auditByIdData.framework.id,
                    frameworkType: auditByIdData.framework.type,
                });
            }

            modalController.openModal({
                id: MODAL_ID,
                content: () => <ChangeSampleEvidenceModal data-id="kSQ-9Mhk" />,
                centered: true,
                size: 'lg',
                disableClickOutsideToClose: true,
            });
        },
    );
};
