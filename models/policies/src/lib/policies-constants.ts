import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import { t } from '@globals/i18n/macro';

export const getPoliciesApprovalStatusOptions =
    (): RadioFieldGroupProps['options'] => [
        {
            label: t({
                message: 'All statuses',
                comment: 'Option for showing all policy approval statuses',
            }),
            value: '',
        },
        {
            label: t({
                message: 'New',
                comment: 'Policy approval status option for new/draft policies',
            }),
            value: 'DRAFT',
        },
        {
            label: t({
                message: 'Needs approval',
                comment:
                    'Policy approval status option for policies needing approval',
            }),
            value: 'NEEDS_APPROVAL',
        },
        {
            label: t({
                message: 'Approved',
                comment: 'Policy approval status option for approved policies',
            }),
            value: 'APPROVED',
        },
        {
            label: t({
                message: 'Published',
                comment: 'Policy approval status option for published policies',
            }),
            value: 'PUBLISHED',
        },
    ];

export const getPoliciesRenewalStatusOptions =
    (): RadioFieldGroupProps['options'] => [
        {
            label: t({
                message: 'All renewal statuses',
                comment: 'Option for showing all policy renewal statuses',
            }),
            value: '',
        },
        {
            label: t({
                message: 'Renewal required soon',
                comment:
                    'Policy renewal status option for policies expiring soon',
            }),
            value: 'EXPIRE_SOON',
        },
        {
            label: t({
                message: 'Renewal past due',
                comment: 'Policy renewal status option for expired policies',
            }),
            value: 'EXPIRED',
        },
    ];

export const getPoliciesStatusOptions = (): {
    id: string;
    label: string;
    value: string;
}[] => [
    {
        id: 'ACTIVE',
        label: t({
            message: 'Active',
            comment: 'Policy status option for active policies',
        }),
        value: 'ACTIVE',
    },
    {
        id: 'ARCHIVED',
        label: t({
            message: 'Archived',
            comment: 'Policy status option for archived policies',
        }),
        value: 'ARCHIVED',
    },
    {
        id: 'REPLACED',
        label: t({
            message: 'Replaced',
            comment: 'Policy status option for replaced policies',
        }),
        value: 'REPLACED',
    },
    {
        id: 'UNACCEPTABLE',
        label: t({
            message: 'Unacceptable',
            comment: 'Policy status option for unacceptable policies',
        }),
        value: 'UNACCEPTABLE',
    },
    {
        id: 'OUTDATED',
        label: t({
            message: 'Outdated',
            comment: 'Policy status option for outdated policies',
        }),
        value: 'OUTDATED',
    },
];
