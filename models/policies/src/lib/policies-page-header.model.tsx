import type { ComponentProps } from 'react';
import { PoliciesPageHeaderActionStack } from '@components/policies';
import {
    sharedPoliciesController,
    sharedPoliciesOwnersController,
} from '@controllers/policies';
import type { Datatable } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type { ListBoxItems } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    getPoliciesApprovalStatusOptions,
    getPoliciesRenewalStatusOptions,
    getPoliciesStatusOptions,
} from './policies-constants';

export class PoliciesPageHeaderModel {
    readonly pageId = 'policies-active';

    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t({
            message: 'Policies',
            comment: 'Page title for policies section',
        });
    }

    get actionStack(): React.JSX.Element {
        return <PoliciesPageHeaderActionStack />;
    }

    get externalPolicyConnection(): boolean {
        return sharedPoliciesController.hasExternalPolicyConnection;
    }

    get hasBambooHrConnection(): boolean {
        const { externalPolicyConnection } = sharedPoliciesController;

        return externalPolicyConnection?.clientType === 'BAMBOO_HR';
    }

    get userSelectFilter(): Filter {
        const { ownersComboboxOptions, isLoading, hasNextPage, loadNextPage } =
            sharedPoliciesOwnersController;

        return {
            filterType: 'combobox',
            id: 'userId',
            label: t({
                message: 'Owner',
                comment: 'Filter label for policy owner',
            }),
            placeholder: t({
                message: 'Search by owner',
                comment: 'Placeholder text for owner search filter',
            }),
            isMultiSelect: false,
            isLoading,
            hasMore: hasNextPage,
            onFetchOptions: loadNextPage,
            options: ownersComboboxOptions,
        };
    }

    get policyApprovalStatusFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'policyApprovalStatus',
            label: t({
                message: 'Status',
                comment: 'Filter label for policy approval status',
            }),
            options: getPoliciesApprovalStatusOptions(),
        };
    }

    get policyStatusFilter(): Filter {
        return {
            filterType: 'combobox',
            id: 'policyStatuses',
            label: t({
                message: 'Policy status',
                comment: 'Filter label for policy status',
            }),
            placeholder: t({
                message: 'Select policy status',
                comment: 'Placeholder text for policy status filter',
            }),
            isMultiSelect: true,
            options: getPoliciesStatusOptions(),
        };
    }

    get filters(): ComponentProps<typeof Datatable>['filterProps'] {
        const { overviewFilter } = sharedPoliciesController;

        // Determine which filter should be set based on the overview filter
        const approvalStatusValue = [
            'NEEDS_APPROVAL',
            'APPROVED',
            'PUBLISHED',
            'DRAFT',
        ].includes(overviewFilter as string)
            ? overviewFilter
            : '';
        const renewalStatusValue = ['EXPIRE_SOON', 'EXPIRED'].includes(
            overviewFilter as string,
        )
            ? overviewFilter
            : '';

        return {
            clearAllButtonLabel: t({
                message: 'Reset',
                comment: 'Button label to clear all filters',
            }),
            onClearFiltersFn: () => {
                sharedPoliciesController.clearOverviewFilter();
            },
            filters: [
                {
                    filterType: 'radio',
                    id: 'policyApprovalStatus',
                    label: t({
                        message: 'Status',
                        comment: 'Filter label for policy approval status',
                    }),
                    value: approvalStatusValue,
                    options: getPoliciesApprovalStatusOptions(),
                },
                {
                    filterType: 'radio',
                    id: 'policyRenewal',
                    label: t({
                        message: 'Renewal status',
                        comment: 'Filter label for policy renewal status',
                    }),
                    value: renewalStatusValue,
                    options: getPoliciesRenewalStatusOptions(),
                },
                this.userSelectFilter,
            ],
        };
    }

    get userSelect(): ListBoxItems {
        return sharedPoliciesOwnersController.ownersComboboxOptions;
    }
}

export const sharedPoliciesPageHeaderModel = new PoliciesPageHeaderModel();
