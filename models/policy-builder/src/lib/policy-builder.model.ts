import { isEmpty } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type { FeedbackProps } from '@cosmos/components/feedback';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { PolicyWithReplaceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate, isDateWithinNextMonths } from '@helpers/date-time';
import { formatAssignedToText } from './helpers/format-assigned-to-text.helper';

class PolicyBuilderModel {
    /**
     * ===== PRIVATE PROPERTIES =====.
     * Internal properties used only within this model.
     */

    private get policy(): PolicyWithReplaceResponseDto | null {
        return sharedPolicyBuilderController.policy;
    }

    private get latestPolicyVersion() {
        return this.policy?.latestPolicyVersion ?? null;
    }

    private get renewalDate(): string | null {
        return this.latestPolicyVersion?.renewalDate ?? null;
    }

    private get hasExpiredRenewalDate(): boolean {
        return this.latestPolicyVersion?.hasExpiredRenewalDate ?? false;
    }

    private get assignedTo(): string {
        return this.policy?.assignedTo ?? '';
    }

    private get groups() {
        return this.policy?.groups ?? [];
    }

    private get policyGracePeriodSLAs() {
        return this.policy?.policyGracePeriodSLAs ?? [];
    }

    private get policyWeekTimeFrameSLAs() {
        return this.policy?.policyWeekTimeFrameSLAs ?? [];
    }

    private get policyP3MatrixSLAs() {
        return this.policy?.policyP3MatrixSLAs ?? [];
    }

    private get linkedControls() {
        return sharedPolicyBuilderController.policyControlsAssociated;
    }

    private get hasLinkedControls(): boolean {
        return !isEmpty(this.linkedControls);
    }

    private get linkedFrameworks() {
        return sharedPolicyBuilderController.policyFrameworksAssociated;
    }

    private get hasLinkedFrameworks(): boolean {
        return !isEmpty(this.linkedFrameworks);
    }

    private get replacedPolicies() {
        return this.policy?.replacedPolicies ?? [];
    }

    /**
     * ===== PUBLIC PROPERTIES =====.
     * Properties exposed to external components.
     */

    get policyName(): string {
        return this.policy?.name ?? '';
    }

    get policyDescription(): string {
        return this.policy?.currentDescription ?? '';
    }

    get assignedToText(): string {
        return formatAssignedToText(this.assignedTo, this.groups);
    }

    get shouldDisplaySLA(): boolean {
        return (
            !isEmpty(this.policyGracePeriodSLAs) ||
            !isEmpty(this.policyWeekTimeFrameSLAs) ||
            !isEmpty(this.policyP3MatrixSLAs)
        );
    }

    get renewalDateDisplay(): {
        value: KeyValuePairProps['value'];
        feedbackProps?: FeedbackProps;
    } {
        if (!this.renewalDate) {
            return {
                value: '-',
            };
        }
        const shouldShowFeedback = isDateWithinNextMonths(2, this.renewalDate);

        if (shouldShowFeedback) {
            const title = formatDate('sentence', this.renewalDate);

            let severity: FeedbackProps['severity'];
            let description: string;

            if (this.hasExpiredRenewalDate) {
                severity = 'critical';
                description = t`This policy is past its renewal date. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            } else {
                severity = 'warning';
                description = t`The renewal date for this policy is coming up soon. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            }

            return {
                value: null,
                feedbackProps: {
                    severity,
                    title,
                    description,
                },
            };
        }

        return {
            value: formatDate('sentence', this.renewalDate),
        };
    }

    get controlsDisplay(): {
        type: KeyValuePairProps['type'];
        value: KeyValuePairProps['value'];
    } {
        if (this.hasLinkedControls) {
            return {
                type: 'TAG',
                value: this.linkedControls.map((control) => ({
                    label: control.code,
                })),
            };
        }

        return {
            type: 'TEXT',
            value: '-',
        };
    }

    get frameworksDisplay(): {
        type: KeyValuePairProps['type'];
        value: KeyValuePairProps['value'];
    } {
        if (this.hasLinkedFrameworks) {
            return {
                type: 'TAG',
                value: this.linkedFrameworks.map((framework) => ({
                    label: framework.pill,
                    colorScheme: 'primary' as const,
                })),
            };
        }

        return {
            type: 'TEXT',
            value: '-',
        };
    }

    get disclaimer(): string {
        return this.policy?.disclaimer ?? '';
    }

    get replacedPoliciesCount(): string {
        return this.replacedPolicies.length.toString();
    }
}

export const sharedPolicyBuilderModel = new PolicyBuilderModel();
