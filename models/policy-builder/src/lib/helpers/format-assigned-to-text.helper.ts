import { isEmpty } from 'lodash-es';
import type { GroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

/**
 * Helper function to display groups as comma-separated string.
 * Matches the groupDisplayHelper from web project.
 */
const groupDisplayHelper = (groups: GroupResponseDto[]): string => {
    if (isEmpty(groups)) {
        return '';
    }

    return groups.map((group) => group.name).join(', ');
};

/**
 * Formats policy assignment data into human-readable text.
 * Matches the logic from web project's getAssignedToText function.
 *
 * @param assignedTo - The assignment type ('ALL', 'GROUP', 'NONE').
 * @param groups - Array of groups when assignedTo is 'GROUP'.
 * @returns Formatted text describing the assignment.
 */
export function formatAssignedToText(
    assignedTo: string,
    groups: GroupResponseDto[],
): string {
    switch (assignedTo) {
        case 'ALL': {
            return t`All personnel`;
        }
        case 'GROUP': {
            return groupDisplayHelper(groups);
        }
        case 'NONE': {
            return t`None`;
        }
        default: {
            return '';
        }
    }
}
