import { isEmpty, isNil } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Banner, type BannerProps } from '@cosmos/components/banner';
import type {
    ContentType,
    KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Metadata, type MetadataProps } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class PolicyBuilderHeaderModel {
    readonly pageId = 'policy-builder';

    constructor() {
        makeAutoObservable(this);
    }

    private get policy() {
        return sharedPolicyBuilderController.policy;
    }

    private get currentVersion() {
        return sharedPolicyBuilderController.currentVersion;
    }

    get title(): string {
        return this.policy?.name || t`Policy Builder`;
    }

    get actionStack(): React.JSX.Element {
        const ActionStackComponent = () => {
            return (
                <div data-testid="ActionStackComponent" data-id="SFUs6qDB">
                    Work in progress
                </div>
            );
        };

        return <ActionStackComponent />;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const pairs = [];

        if (this.currentVersion?.createdAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Creation date`,
                    'policy-creation-date',
                    this.currentVersion.createdAt,
                ),
            );
        }
        if (this.currentVersion?.approvedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Approved date`,
                    'policy-approved-date',
                    this.currentVersion.approvedAt,
                ),
            );
        }
        if (this.currentVersion?.publishedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Published date`,
                    'policy-published-date',
                    this.currentVersion.publishedAt,
                ),
            );
        }
        if (!isNil(this.currentVersion?.updatedAt)) {
            pairs.push(
                this.buildKeyValue(
                    t`Last saved`,
                    'policy-last-saved',
                    this.currentVersion.updatedAt,
                ),
            );
        }

        return pairs;
    }

    get slotTopAlign(): React.JSX.Element | null {
        const items = [];

        if (this.currentVersion?.policyVersionStatus) {
            items.push(
                <Metadata
                    key="status"
                    type="tag"
                    label={this.getStatusLabel(
                        this.currentVersion.policyVersionStatus,
                    )}
                    colorScheme={this.getStatusColor(
                        this.currentVersion.policyVersionStatus,
                    )}
                />,
            );
        }

        if (this.policy?.policyStatus) {
            items.push(
                <Metadata
                    key="policy-status"
                    label={this.policy.policyStatus}
                    type="tag"
                    colorScheme={this.getPolicyStatusColor(
                        this.policy.policyStatus,
                    )}
                />,
            );
        }

        if (
            this.currentVersion?.composedVersion ||
            this.currentVersion?.policyVersionStatus === 'DRAFT'
        ) {
            items.push(
                <Metadata
                    key="version"
                    label={this.versionLabel}
                    colorScheme="neutral"
                    type="tag"
                />,
            );
        }

        if (isEmpty(items)) {
            return null;
        }

        return (
            <Stack direction="row" gap="2x">
                {items}
            </Stack>
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { policy, currentVersion } = this;

        const conditions: {
            when: unknown;
            data: {
                severity: BannerProps['severity'];
                title: string;
                body: string;
            };
        }[] = [
            {
                when: !policy?.currentOwner,
                data: {
                    severity: 'critical',
                    title: t`Owner is missing`,
                    body: t`Drata requires an owner for all policies.`,
                },
            },
            {
                when:
                    currentVersion?.hasExpiredRenewalDate &&
                    ['DRAFT', 'NEEDS_APPROVAL', 'APPROVED'].includes(
                        currentVersion.policyVersionStatus,
                    ),
                data: {
                    severity: 'critical',
                    title: t`Update renewal date`,
                    body: t`Before finalizing, make sure your renewal date is updated.`,
                },
            },
            {
                when: currentVersion?.externalProvider,
                data: {
                    severity: 'warning',
                    title: t`External policy sync`,
                    body: t`This policy is synced with an external source.`,
                },
            },
            {
                when:
                    currentVersion?.policyVersionStatus === 'DRAFT' &&
                    currentVersion.approvedAt,
                data: {
                    severity: 'warning',
                    title: t`Changes requested`,
                    body: t`Changes have been requested for this policy version.`,
                },
            },
            {
                when:
                    policy?.currentPublishedPolicyVersion?.id &&
                    ['DRAFT', 'PUBLISHED'].includes(
                        currentVersion?.policyVersionStatus ?? '',
                    ) &&
                    policy.currentPublishedPolicyVersion.id !==
                        policy.latestPolicyVersion?.id,
                data: {
                    severity: 'primary',
                    title: t`Policy version notice`,
                    body: t`There is a newer version of this policy available.`,
                },
            },
        ];

        const matchedCondition = conditions.find((c) => c.when);

        if (!matchedCondition) {
            return undefined;
        }

        const { severity, title, body } = matchedCondition.data;

        return <Banner severity={severity} title={title} body={body} />;
    }

    private get versionLabel() {
        if (this.currentVersion?.policyVersionStatus === 'DRAFT') {
            return t`Draft`;
        }
        if (this.currentVersion?.composedVersion) {
            const version = this.currentVersion.composedVersion;

            return t`Version ${version}`;
        }

        return t`Version 1.0`;
    }

    private getStatusColor(status: string) {
        const map: Record<string, MetadataProps['colorScheme']> = {
            NEEDS_APPROVAL: 'warning',
            APPROVED: 'success',
            PUBLISHED: 'success',
        };

        return map[status];
    }

    private getPolicyStatusColor(status: string): MetadataProps['colorScheme'] {
        const map: Record<string, MetadataProps['colorScheme']> = {
            ACTIVE: 'success',
            ARCHIVED: 'critical',
            REPLACED: 'critical',
        };

        return map[status];
    }

    private getStatusLabel(status: string) {
        const map: Record<string, string> = {
            NEEDS_APPROVAL: t`Needs Approval`,
            APPROVED: t`Approved`,
            PUBLISHED: t`Published`,
        };

        return map[status] || status;
    }

    private buildKeyValue(
        label: string,
        id: string,
        date: string,
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: new Date(date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            }),
            type: 'TEXT' as ContentType,
        };
    }
}
