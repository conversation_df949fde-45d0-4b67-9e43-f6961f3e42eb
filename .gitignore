*.log
**/generated
globals/i18n/messages
globals/i18n/messages

# Legacy documentation (replaced by .augment/ structure)
.augment-guidelines

.eslintcache
.swc

/api

# React Router
/.react-router/
/build/

profile.json

# Cloudflare
.mf
.wrangler

# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
apps/drata/routes-output.json
scripts/all-routes.ts

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data

vite.config.*.timestamp*
vitest.config.*.timestamp*

storybook-static

# Token build
cosmos/constants/tokens/src/build

*storybook.log

# Dependency Update MVP
package.json.backup.*
