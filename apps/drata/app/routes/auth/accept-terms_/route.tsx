import { sharedCurrentUserController } from '@globals/current-user';
import { action, observer, when } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, redirect } from '@remix-run/react';
import { AcceptTermsView } from '@views/accept-terms';
import { LoginLayoutView } from '@views/login';

export const meta: MetaFunction = () => {
    return [{ title: 'Accept Terms' }];
};

export const clientLoader: ClientLoaderFunction = action(async() => {
    const { hasAcceptedTerms, homePagePathname, isLoading } =
        sharedCurrentUserController;

    const nextRoute = await new Promise((resolve)=>{
        when(
                () => !isLoading,
                () => {
                    // Now you can safely check hasAcceptedTerms
                    if (hasAcceptedTerms) {
                        resolve(redirect(homePagePathname, 302));
                    }

                    resolve(null);
                },
            );
    })

    

});

export const AcceptTerms = observer((): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="AcceptTerms" data-id="xUwZvlVN">
            <AcceptTermsView />
        </LoginLayoutView>
    );
});

export default AcceptTerms;
