import type { ClientLoader } from '@app/types';
import { sharedAuditHubController } from '@controllers/audit-hub';
import {
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedAuditorFrameworkEvidenceQueryController,
    sharedCompanyArchiveStatusQueryController,
    sharedCompanyStatsQueryController,
    sharedCompanySummaryQueryController,
    sharedConnectionInfoQueryController,
    sharedTotalEvidenceQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditHubAuditorValidatePersonnelController } from '@controllers/audit-hub-auditor-validate-personnel';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { AuditorClientAuditModel } from '@models/auditor-client-audit';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditorClientAuditDetailsView } from '@views/auditor-client-audit-details';

export const meta: MetaFunction = () => [
    { title: t`Audit Hub - Audit Details` },
];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId, auditId } = params;

        if (clientId && auditId) {
            sharedAuditHubController.loadAuditById(auditId);

            sharedCustomerRequestsController.frameworkId = auditId;
            sharedAuditorFrameworkEvidenceQueryController.loadLatestAuditorFrameworkEvidencePingQuery(
                auditId,
            );
            sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatus();
            sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus();
            sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatusByArchiveStatus();
            sharedConnectionInfoQueryController.loadConnectionInfo();
            sharedCompanySummaryQueryController.loadCompanySummary();

            sharedAuditHubAuditorClientAuditController.loadAuditDates(auditId);
            sharedAuditHubAuditorClientAuditController.loadAuditors(auditId);
            sharedAuditorController.auditSummaryByIdQuery.load({
                path: { auditorFrameworkId: auditId },
            });

            sharedCustomerRequestsController.customerRequestListQuery.load({
                query: { framework: auditId },
            });
            sharedCompanyStatsQueryController.loadStats();
            sharedTotalEvidenceQueryController.handleGetTotalEvidenceByFrameworkId(
                auditId,
            );
            sharedAuditHubAuditorValidatePersonnelController.validatePersonnelStartDateEndDate(
                auditId,
            );

            return {
                pageHeader: new AuditorClientAuditModel(),
                utilities: {
                    utilitiesList: [],
                },
            };
        }

        return null;
    },
);

const AuditorClientAuditDetails = (): React.JSX.Element => {
    return (
        <AuditorClientAuditDetailsView
            data-testid="AuditorClientAuditDetails"
            data-id="auditClientDetails"
        />
    );
};

export default AuditorClientAuditDetails;
