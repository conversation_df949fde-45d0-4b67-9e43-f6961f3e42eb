import type { ClientLoader } from '@app/types';
import { sharedAccessReviewUserController } from '@controllers/access-reviews';
import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { action } from '@globals/mobx';
import { AccessReviewApplicationsPersonnelDetailsModel } from '@models/access-review-applications-personnel-details';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AppLibraryPersonnelDetails } from '@views/access-review-application-details';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { applicationId, userId } = params;

        if (!applicationId || !userId) {
            throw new Error(
                'Access Review ApplicationId and userId is required',
            );
        }

        sharedAccessReviewUserController.loadAccessReviewUser({
            applicationId,
            userId,
        });

        activeAccessReviewsApplicationsController.loadAccessReviewApplicationDetails(
            Number(applicationId),
        );

        return {
            pageHeader: new AccessReviewApplicationsPersonnelDetailsModel(),
            tabs: [],
        };
    },
);

export const meta: MetaFunction = () => [{ title: 'Access Review Details' }];

const AccessReviewApplicationDetailsPersonnelDetails =
    (): React.JSX.Element => {
        return (
            <AppLibraryPersonnelDetails
                data-testid="AccessReviewApplicationDetailsPersonnelDetails"
                data-id="PQKKCe-u"
            />
        );
    };

export default AccessReviewApplicationDetailsPersonnelDetails;
