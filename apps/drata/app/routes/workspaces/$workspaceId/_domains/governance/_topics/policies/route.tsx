import type { ClientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedPoliciesController,
    sharedPoliciesOwnersController,
} from '@controllers/policies';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Policies' }];

export const clientLoader = action((): ClientLoader => {
    sharedPoliciesController.overview.load();
    sharedPoliciesOwnersController.initializePoliciesOwners();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    sharedUsersController.loadPolicyManagementUsers();

    return {
        pageHeader: sharedPoliciesPageHeaderModel,
        tabs: [
            {
                id: 'governance.policies.active',
                topicPath: 'governance/policies/active',
                label: t`Active`,
            },
            {
                id: 'governance.policies.archive',
                topicPath: 'governance/policies/archive',
                label: t`Archive and replaced`,
            },
        ],
    };
});

const Policies = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Policies" data-id="MviHNu9x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Policies;
