import type { ClientLoader } from '@app/types';
import { sharedPoliciesController } from '@controllers/policies';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { PoliciesActiveView } from '@views/policies';

export const clientLoader = action((): ClientLoader => {
    sharedPoliciesController.loadActivePolicies({
        globalFilter: { search: '', filters: {} },
        pagination: {
            pageIndex: 0,
            pageSize: DEFAULT_PAGE_SIZE,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        sorting: [],
    });

    return {
        pageHeader: sharedPoliciesPageHeaderModel,
    };
});

const PoliciesActive = (): React.JSX.Element => {
    return (
        <PoliciesActiveView data-testid="PoliciesActive" data-id="d_yY5uG5" />
    );
};

export default PoliciesActive;
