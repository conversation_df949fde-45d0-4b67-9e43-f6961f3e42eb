import type { ClientLoader } from '@app/types';
import { routeController } from '@controllers/route';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { AddAssetView } from '@views/add-asset';

export const meta: MetaFunction = () => [{ title: t`Add an asset` }];

export const clientLoader = action((): ClientLoader => {
    sharedUsersController.usersList.load();

    return {
        pageHeader: {
            title: t`Add an asset`,
            pageId: 'add-asset-page-header',
            backLink: (
                <AppLink
                    data-id="add-asset-page-header-back-link"
                    href={`${routeController.userPartOfUrl}/risk/assets`}
                    label={t`Back to Assets`}
                />
            ),
        },
    };
});

const AddRiskAsset = (): React.JSX.Element => {
    return <AddAssetView data-testid="AddRiskAsset" data-id="vJtg_bBY" />;
};

export default AddRiskAsset;
