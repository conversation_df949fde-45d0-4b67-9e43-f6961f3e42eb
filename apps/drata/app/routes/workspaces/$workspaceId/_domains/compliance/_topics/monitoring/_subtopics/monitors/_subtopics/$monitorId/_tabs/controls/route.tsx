import type { ClientLoader } from '@app/types';
import {
    activeMonitoringCodeDetailsController,
    sharedMonitoringDetailsControlsController,
} from '@controllers/monitoring-details';
import { action } from '@globals/mobx';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringDetailsControlsView } from '@views/monitoring-details-controls';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { monitorId } = params;

        sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
            Number(monitorId),
        );
        activeMonitoringCodeDetailsController.loadTest(Number(monitorId));

        return null;
    },
);

const MonitoringDetailsControls = (): React.JSX.Element => {
    return (
        <MonitoringDetailsControlsView
            data-testid="MonitoringDetailsControls"
            data-id="AW9o6hGx"
        />
    );
};

export default MonitoringDetailsControls;
