import type { <PERSON>lientLoader } from '@app/types';
import {
    activeMonitoringController,
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringPersonnelExclusionsController,
} from '@controllers/monitoring-details';
import { action } from '@globals/mobx';
import { MonitoringDetailsPageHeaderModel } from '@models/monitoring-details';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { monitorId } = params;

        activeMonitoringController.loadMonitor(Number(monitorId));

        sharedMonitoringDetailsExclusionsController.setTestId(
            Number(monitorId),
        );

        sharedMonitoringDetailsExclusionsController.loadExclusions();

        sharedMonitoringPersonnelExclusionsController.setTestId(
            Number(monitorId),
        );

        sharedMonitoringPersonnelExclusionsController.loadPersonnelExclusions();

        return {
            pageHeader: new MonitoringDetailsPageHeaderModel(),
            tabs: [
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/overview`,
                    label: 'Overview',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/findings`,
                    label: 'Findings',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/exclusions`,
                    label: 'Exclusions',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/controls`,
                    label: 'Controls',
                },
            ],
        };
    },
);

const MonitoringMonitorDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="MonitoringMonitorDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default MonitoringMonitorDetails;
