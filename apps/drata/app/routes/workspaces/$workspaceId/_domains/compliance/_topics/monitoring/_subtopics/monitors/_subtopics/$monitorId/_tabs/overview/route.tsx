import { sharedConnectionsController } from '@controllers/connections';
import { sharedEventsController } from '@controllers/events';
import {
    activeMonitoringController,
    activeTicketsMetadataController,
    activeTrackCardController,
    sharedMonitorFindingsController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { action } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { MonitoringDetailsOverviewView } from '@views/monitoring-details-overview';

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): null => {
        const { monitorId } = params;

        activeMonitoringController.loadMonitor(Number(monitorId));
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedEventsController.loadEvents();
        sharedMonitoringTestDetailsController.loadTest(Number(monitorId));
        activeTicketsMetadataController.loadTicketsMetadata(Number(monitorId));
        activeTrackCardController.loadTrackData(Number(monitorId));
        sharedMonitorFindingsController.loadFindingsResponse(Number(monitorId));

        return null;
    },
);

const MonitoringDetailsOverview = (): React.JSX.Element => {
    return (
        <MonitoringDetailsOverviewView
            data-testid="MonitoringDetailsOverview"
            data-id="monitoring-details-overview"
        />
    );
};

export default MonitoringDetailsOverview;
