import { debounce } from 'lodash-es';
import { type Change<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, use<PERSON>emo, useState } from 'react';
import { styled } from 'styled-components';
import { Search } from '@cosmos/components/search';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table } from '@tanstack/react-table';
import { DEFAULT_SEARCH_VALUE } from '../../constants/default-global-filter-value.constant';
import { DEFAULT_TABLE_SEARCH_PROPS } from '../../constants/default-table-search-props.constant';
import { TABLE_ID } from '../../constants/table-id.constant';

const StyledScreenReaderLabel = styled.label`
    opacity: 0;
    height: 0;
    width: 0;
    overflow: hidden;
    position: absolute;
`;

interface TableSearchProps<TData extends RowData> {
    debounceDelay?: number;
    onChange: Table<TData>['setGlobalFilter'];
    onClear: Table<TData>['setGlobalFilter'];
    placeholder?: string;
    defaultValue?: string;
}

/**
 * NOTE: The main purpose of this component is to isolate and memoize the debounce behavior.
 *
 * We need to build this in a way that the value can be set/reset from anywhere
 * this is not easy when we also need a debounced function with a controlled local state
 * we could run useEffect on the global state filter value watching for changes, then update the local state value here
 * but watching the global state introduces more chances for re-render issues
 * which I was wanting to avoid by encapsulating global search into its own component.
 */
export const TableSearch = <TData extends RowData>({
    debounceDelay = DEFAULT_TABLE_SEARCH_PROPS.debounceDelay,
    onChange,
    onClear,
    placeholder = DEFAULT_TABLE_SEARCH_PROPS.placeholder,
    defaultValue = DEFAULT_TABLE_SEARCH_PROPS.defaultValue,
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- need this
}: TableSearchProps<TData>) => {
    const [value, setValue] = useState<string>(defaultValue);

    const debouncedOnChange = useMemo(
        () => debounce(onChange, debounceDelay),
        [debounceDelay, onChange],
    );

    const handleSetGlobalFilter: ChangeEventHandler<HTMLInputElement> =
        useCallback(
            ({ target: { value: newValue } }) => {
                // Trim whitespace before passing to search filter to prevent blank space searches
                const trimmedValue = newValue.trim();

                setValue(trimmedValue);
                debouncedOnChange(trimmedValue);
            },
            [debouncedOnChange, setValue],
        );

    const handleResetGlobalFilter = useCallback(() => {
        setValue(DEFAULT_SEARCH_VALUE);
        onClear(DEFAULT_SEARCH_VALUE);
    }, [onClear, setValue]);

    const GLOBAL_FILTER_LABEL_ID = 'datatable-global-filter-input';

    return (
        <>
            <StyledScreenReaderLabel id={GLOBAL_FILTER_LABEL_ID}>
                Global Filter
            </StyledScreenReaderLabel>
            <Search
                aria-controls={TABLE_ID}
                aria-labelledby={GLOBAL_FILTER_LABEL_ID}
                clearSearchButtonLabel="Clear Global Filter"
                id="global-search"
                name="global-search"
                placeholder={placeholder}
                value={value}
                onChange={handleSetGlobalFilter}
                onClear={handleResetGlobalFilter}
            />
        </>
    );
};
