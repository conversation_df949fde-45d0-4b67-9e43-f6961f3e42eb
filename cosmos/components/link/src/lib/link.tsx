import { isEmpty } from 'lodash-es';
import { styled } from 'styled-components';
import { Icon } from '@cosmos/components/icon';
import { Text } from '@cosmos/components/text';
import {
    borderRadiusSm,
    borderWidthMd,
    dimension0x,
    dimensionXs,
    primaryBorderFocus,
} from '@cosmos/constants/tokens';
import { stateToCSS } from '@cosmos/helpers/state-to-css';
import { LINK_COLOR_SCHEMES, LINK_SIZE } from './constants';
import type { LinkColorSchemes, LinkSize } from './types';

const StyledA = styled.a<{
    $gap?: string;
    $colorScheme: LinkColorSchemes[keyof LinkColorSchemes];
}>`
    &:visited {
        ${({ $colorScheme }) => stateToCSS($colorScheme.default)}
    }

    &:hover {
        ${({ $colorScheme }) =>
            $colorScheme.hover && stateToCSS($colorScheme.hover)}
    }

    &:focus-visible {
        border-radius: ${borderRadiusSm};
        outline-offset: ${dimensionXs};
        outline: ${borderWidthMd} solid ${primaryBorderFocus};
    }

    ${({ $colorScheme }) => stateToCSS($colorScheme.default)}

    cursor: pointer;
    display: inline-flex;
    gap: ${dimensionXs};
`;

const StyledLabelSpan = styled.span`
    text-decoration: underline;
`;

const StyledIconWrapper = styled.div<{
    $size: LinkSize;
}>`
    margin-top: ${({ $size }) => ($size === 'sm' ? '1px' : dimension0x)};
`;

export type LinkProps<C extends React.ElementType = 'a'> = {
    /**
     * The optional external routing component to render.
     */
    component?: C;

    'data-id'?: string;

    /**
     * Which color scheme to apply.
     */
    colorScheme?: 'primary' | 'success' | 'warning' | 'critical' | 'inverted';

    /**
     * The URI to provide to the anchor tag
     * Omit if you will handle navigation within the `onClick` handler (e.g. With `goto`).
     */
    href?: string;

    /**
     * External links are indicated as such with an icon on the right side of the text.
     */
    isExternal?: boolean;

    /**
     * The text to display
     * Displays the `href` property, if not specified.
     */
    label?: string;

    /**
     * A function to run when the Link is clicked.
     */
    onClick?: React.MouseEventHandler<HTMLAnchorElement>;

    /**
     * Used to determine the size to use for the underlying `Text` and `Icon`
     * see: `LINK_SIZE`.
     */
    size?: LinkSize;
} & React.ComponentPropsWithoutRef<C>;

/**
 * Links are clickable elements we use in the application and our web properties that takes the
 * user to an internal or external web page.
 */
export const Link = <C extends React.ElementType = 'a'>({
    colorScheme = 'primary',
    component: Component = undefined,
    'data-id': dataId,
    href,
    isExternal = false,
    label = undefined,
    size = 'md',
    ...restProps
}: LinkProps<C>): React.JSX.Element => {
    return (
        <StyledA
            $colorScheme={LINK_COLOR_SCHEMES[colorScheme]}
            data-id={dataId}
            href={href}
            rel="noopener noreferrer"
            target={isExternal ? '_blank' : '_self'}
            as={Component}
            {...restProps}
            data-testid="Link"
        >
            <StyledLabelSpan>
                <Text
                    size={LINK_SIZE[size].textSize}
                    colorScheme="inherit"
                    as="span"
                >
                    {!label || isEmpty(label) ? href : label}
                </Text>
            </StyledLabelSpan>

            {isExternal && (
                <StyledIconWrapper
                    $size={size}
                    data-id={`${dataId}-icon-wrapper`}
                >
                    <Icon
                        name="LinkOut"
                        size={LINK_SIZE[size].iconSize}
                        colorScheme="inherit"
                    />
                </StyledIconWrapper>
            )}
        </StyledA>
    );
};
