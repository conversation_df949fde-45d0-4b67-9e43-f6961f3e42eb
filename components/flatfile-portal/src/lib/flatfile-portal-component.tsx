import { isError, isNil } from 'lodash-es';
import { useEffect } from 'react';
import {
    calculateScores,
    closeFlatfilePortalModal,
    FLATFILE_IMPORT_MODAL_ID,
    getValidationSchema,
    transformRecordData,
    validateFields,
} from '@components/flatfile-portal';
import {
    FlatfileEntityType,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { sharedFlatfileJobsController } from '@controllers/flatfile-jobs';
import { modalController } from '@controllers/modal';
import type { Flatfile } from '@flatfile/api';
import type { FlatfileListener } from '@flatfile/listener';
import { type FlatfileRecord, recordHook } from '@flatfile/plugin-record-hook';
import { Space, useFlatfile, useListener, usePlugin } from '@flatfile/react';
import { logger } from '@globals/logger';
import { when } from '@globals/mobx';

interface Props {
    spaceId: string;
    workbook: Flatfile.Workbook;
}

/**
 * Flatfile Portal component that handles file import functionality.
 * Removed MobX action wrapper to comply with React hooks best practices.
 * Uses useEffect for proper side effect management and cleanup.
 */
export const FlatfilePortal = ({ spaceId, workbook }: Props): JSX.Element => {
    const [principalSheet] = workbook.sheets ?? [];
    const sheetIdSlug = principalSheet.slug || '';
    const { recordKeys, shape: validations } = getValidationSchema(sheetIdSlug);
    const CLOSE_DELAY = 4_500;

    const { openPortal } = useFlatfile({
        onClose: () => {
            modalController.closeModal(FLATFILE_IMPORT_MODAL_ID);
        },
    });

    /**
     * Set up job:ready event listener.
     */
    const handleJobReady = async (info: { context: { jobId: string } }) => {
        try {
            const { context } = info;
            const { jobId } = context as { jobId: string };

            if (isNil(workbook.sheets)) {
                logger.error({
                    message: 'Failed to import records, sheetId is undefined',
                    additionalInfo: { jobId },
                });

                sharedFlatfileJobsController.completeJob(jobId, false);

                return;
            }

            const workbookSheetId = workbook.sheets[0].id;

            // Check if job is already completed
            if (sharedFlatfileJobsController.isJobCompleted(jobId)) {
                logger.info({
                    message: 'Job already completed, skipping processing',
                    additionalInfo: { jobId },
                });

                return;
            }

            sharedFlatfileJobsController.ackJob(jobId, {
                info: 'Starting import job',
                progress: 20,
            });

            sharedFlatfileController.bulkImport({
                sheetId: workbookSheetId,
                spaceId,
                entityType: FlatfileEntityType.RISK,
            });

            // Use MobX's when to wait for the import to complete with cancellation support
            await when(() => !sharedFlatfileController.isImporting);

            const { response } = sharedFlatfileController.bulkImportMutation;
            const error = sharedFlatfileController.importError;

            if (error) {
                logger.error({
                    message: 'Failed to import records',
                    errorObject: {
                        message: error.message,
                        statusCode: '500',
                    },
                });

                sharedFlatfileJobsController.completeJob(jobId, false);

                return;
            }

            if (!response) {
                sharedFlatfileJobsController.completeJob(jobId, false);

                return;
            }

            // Complete job with success or partial success
            sharedFlatfileJobsController.completeJob(jobId, true, {
                successCount: response.successCount,
                failureCount: response.failureCount,
                sheetId: workbookSheetId,
            });

            if (response.failureCount === 0) {
                // Close modal after a short delay
                setTimeout(closeFlatfilePortalModal, CLOSE_DELAY);
            }
        } catch (error) {
            logger.error({
                message: 'Error handling Flatfile job',
                errorObject: {
                    message: isError(error) ? error.message : String(error),
                    statusCode: '500',
                },
            });
        }
    };

    useListener(
        (listener: FlatfileListener) => {
            listener.on(
                'job:ready',
                { job: 'workbook:submitAction' },
                handleJobReady,
            );
        },
        [handleJobReady],
    );

    // Set up record hook for data transformation and validation
    usePlugin(
        recordHook(sheetIdSlug, (record: FlatfileRecord) => {
            // Transform data before validation
            transformRecordData(record, recordKeys);

            // Calculate and set scores
            calculateScores(record);

            // Validate fields and add errors
            validateFields(record, recordKeys, validations);

            return record;
        }),
        [sheetIdSlug, recordKeys, validations],
    );

    // Set up portal opening in useEffect
    useEffect(() => {
        // Open the portal
        openPortal();
    }, [workbook, spaceId, openPortal]);

    return (
        <Space id={spaceId} data-id="Bfg-WvKa" data-testid="FlatfilePortal" />
    );
};
