import { isEmpty } from 'lodash-es';
import { REVIEW_APPROVAL_STATUS } from '@components/controls';
import {
    sharedControlApprovalReviewersController,
    sharedControlApprovalsController,
    sharedControlDetailsController,
    sharedControlFrameworksController,
    sharedControlOwnersController,
    sharedControlRisksController,
    sharedControlsController,
    sharedControlsDetailsStatsController,
} from '@controllers/controls';
import {
    sharedEvidenceDetailsController,
    sharedEvidenceMutationController,
} from '@controllers/evidence-library';
import {
    sharedRequirementDetailsController,
    sharedRequirementDissociateControlsMutationController,
} from '@controllers/requirements';
import type { ButtonProps } from '@cosmos/components/button';
import type { BaseMetadataProps } from '@cosmos/components/metadata';
import type { AvatarStackProps } from '@cosmos-lab/components/avatar-stack';
import type { DataDonutProps } from '@cosmos-lab/components/data-donut';
import type {
    ApprovalUserResponseDto,
    ControlListResponseDto,
    ControlMonitorResponseDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import type { ControlPanelDonutChart } from '../../types/control-panel-donut-chart.types';
import type { ControlPanelSource } from '../control-panel-props.type';
import { getDonutValues } from '../helpers/get-donut-values';

export class ControlPanelModel {
    constructor() {
        makeAutoObservable(this);
    }

    controlPanelSource: ControlPanelSource | undefined;

    setControlPanelSource = (controlPanelSource: ControlPanelSource): void => {
        this.controlPanelSource = controlPanelSource;
    };

    get controlsToUse():
        | ControlMonitorResponseDto[]
        | RiskResponseDto[]
        | ControlListResponseDto[] {
        switch (this.controlPanelSource) {
            case 'EVIDENCE': {
                return sharedEvidenceDetailsController.evidenceDetailsLinkedControls;
            }
            case 'RISKS': {
                return sharedControlRisksController.controlRisks;
            }
            case 'FRAMEWORK_REQUIREMENTS': {
                return sharedControlsController.controls;
            }
            default: {
                return [];
            }
        }
    }

    get currentControlIndex(): number {
        return this.controlsToUse.findIndex(
            (control) =>
                control.id === sharedControlDetailsController.controlId,
        );
    }

    get controlReviewers(): {
        id: string;
        reviewer: ApprovalUserResponseDto;
        isApproved: boolean;
    }[] {
        const { currentControlApproval } = sharedControlApprovalsController;
        const { controlApprovalsReviewers } =
            sharedControlApprovalReviewersController;

        const isApproved =
            currentControlApproval?.approvalStatus ===
            REVIEW_APPROVAL_STATUS.COMPLETED;

        if (!currentControlApproval) {
            return [];
        }

        return controlApprovalsReviewers.map((reviewer) => {
            const approvalStatus = isApproved
                ? REVIEW_APPROVAL_STATUS.COMPLETED
                : false;

            return {
                id: `${reviewer.id}`,
                reviewer,
                isApproved: approvalStatus
                    ? currentControlApproval.updatedBy?.id === reviewer.id
                    : false,
            };
        });
    }

    get owners(): AvatarStackProps['avatarData'] {
        const { controlOwners } = sharedControlOwnersController;

        return controlOwners.map((owner) => {
            const { firstName, lastName, avatarUrl, email } = owner;
            const fullName = getFullName(firstName, lastName);

            return {
                fallbackText: getInitials(fullName),
                primaryLabel: fullName,
                secondaryLabel: email,
                imgSrc: avatarUrl ?? undefined,
            };
        });
    }

    get frameworksTags(): BaseMetadataProps[] {
        const { allControlFrameworks } = sharedControlFrameworksController;

        if (isEmpty(allControlFrameworks)) {
            return [];
        }

        const frameworksWithPotentialDuplicates = allControlFrameworks.map(
            (framework) => framework.frameworkPill,
        );

        const uniqueFrameworks = [
            ...new Set(frameworksWithPotentialDuplicates),
        ];

        return uniqueFrameworks.map((frameworkPill) => ({
            label: frameworkPill,
            colorScheme: 'neutral',
        }));
    }

    get evidenceDonutValues(): DataDonutProps['values'] {
        return getDonutValues(
            sharedControlsDetailsStatsController.controlEvidenceStats,
        );
    }

    get shouldDisplayMonitoringDonut(): boolean {
        const { controlDetails } = sharedControlDetailsController;
        const { isMapControlsTestsEnabled } = sharedEntitlementFlagController;

        return (
            isMapControlsTestsEnabled || (controlDetails?.isMonitored ?? false)
        );
    }

    get monitoringDonutValues(): DataDonutProps['values'] {
        return getDonutValues(
            sharedControlsDetailsStatsController.controlMonitorsStats,
        );
    }

    get policiesDonutValues(): DataDonutProps['values'] {
        return getDonutValues(
            sharedControlsDetailsStatsController.controlPoliciesStats,
        );
    }

    get panelFooterActions(): ButtonProps[] {
        const { controlDetails } = sharedControlDetailsController;
        const { unlinkControl: unlinkEvidenceControl } =
            sharedEvidenceMutationController;
        const { unlinkControl: unlinkRequirementControl } =
            sharedRequirementDissociateControlsMutationController;
        const { requirement } = sharedRequirementDetailsController;
        const { hasWriteEvidenceLibraryPermission, hasWriteControlPermission } =
            sharedFeatureAccessModel;

        if (!controlDetails) {
            return [];
        }

        switch (this.controlPanelSource) {
            case 'EVIDENCE': {
                if (!hasWriteEvidenceLibraryPermission) {
                    return [];
                }

                return [
                    {
                        label: t`Unmap control`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: action(() => {
                            unlinkEvidenceControl({
                                id: controlDetails.id,
                                code: controlDetails.code,
                            });
                        }),
                    },
                ];
            }
            case 'RISKS': {
                return [];
            }
            case 'FRAMEWORK_REQUIREMENTS': {
                if (!hasWriteControlPermission || !requirement) {
                    return [];
                }

                return [
                    {
                        label: t`Unmap control`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: action(() => {
                            unlinkRequirementControl(
                                controlDetails.id,
                                requirement.id,
                            );
                        }),
                    },
                ];
            }
            default: {
                return [];
            }
        }
    }

    get donutsValuesToRender(): ControlPanelDonutChart[] {
        return this.donutConfigs.filter(
            ({ show, values }) =>
                show && values.some((slice) => slice.value > 0),
        );
    }

    private get donutConfigs(): ControlPanelDonutChart[] {
        return [
            {
                key: 'evidence',
                label: t`Evidence`,
                values: this.evidenceDonutValues,
                iconName: 'EvidenceLibrary',
                'data-id': 'control-evidence-donut',
                show: true,
            },
            {
                key: 'monitoring',
                label: t`Monitoring`,
                values: this.monitoringDonutValues,
                iconName: 'Monitoring',
                'data-id': 'control-monitoring-donut',
                show: this.shouldDisplayMonitoringDonut,
            },
            {
                key: 'policies',
                label: t`Policies`,
                values: this.policiesDonutValues,
                iconName: 'PolicyCenter',
                'data-id': 'control-policies-donut',
                show: true,
            },
        ];
    }
}

export const sharedControlPanelModel = new ControlPanelModel();
