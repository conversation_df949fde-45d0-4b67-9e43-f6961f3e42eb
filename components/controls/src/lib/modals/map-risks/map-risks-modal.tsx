import { sharedControlRisksMutationController } from '@controllers/controls';
import { modalController } from '@controllers/modal';
import type { RiskListBoxItemData } from '@controllers/risk';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { MAP_RISKS_MODAL_ID } from './constants/map-risks-modal-id.constant';
import { getFormSchema } from './helpers/get-form-schema.helper';

export const MapRisksModal = observer(
    ({ controlId }: { controlId: number }) => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleSubmit = action((values: FormValues) => {
            const typedValues = values as {
                risks: RiskListBoxItemData[];
            };

            const risksCodes = typedValues.risks.map(
                ({ riskCode }) => riskCode,
            );

            sharedControlRisksMutationController.mapRisksToControl(
                controlId,
                risksCodes,
            );

            when(
                () => !sharedControlRisksMutationController.isMappingPending,
                () => {
                    modalController.closeModal(MAP_RISKS_MODAL_ID);
                },
            );
        });

        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close`}
                    title={t`Map risks`}
                    onClose={() => {
                        modalController.closeModal(MAP_RISKS_MODAL_ID);
                    }}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="map-risks-form"
                        data-id="map-risks-form"
                        schema={getFormSchema()}
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled:
                                sharedControlRisksMutationController.isMappingPending,
                            onClick: () => {
                                modalController.closeModal(MAP_RISKS_MODAL_ID);
                            },
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            colorScheme: 'primary',
                            type: 'submit',
                            isLoading:
                                sharedControlRisksMutationController.isMappingPending,
                            onClick: () => {
                                triggerSubmit().catch(() => {
                                    console.error('Failed to submit form');
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
