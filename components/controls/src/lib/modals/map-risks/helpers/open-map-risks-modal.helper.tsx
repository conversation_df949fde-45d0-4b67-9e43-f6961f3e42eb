import { modalController } from '@controllers/modal';
import { sharedRiskExclusionsInfiniteController } from '@controllers/risk';
import { action } from '@globals/mobx';
import { MAP_RISKS_MODAL_ID } from '../constants/map-risks-modal-id.constant';
import { MapRisksModal } from '../map-risks-modal';

export const openMapRisksModal = action(
    ({ controlId }: { controlId: number }): void => {
        sharedRiskExclusionsInfiniteController.loadRisksWithExclusions({
            excludeControlId: controlId,
        });

        modalController.openModal({
            id: MAP_RISKS_MODAL_ID,
            content: () => (
                <MapRisksModal controlId={controlId} data-id="Gk4V-eoz" />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    },
);
