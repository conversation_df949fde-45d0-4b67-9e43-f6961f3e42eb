import { sharedRiskExclusionsInfiniteController } from '@controllers/risk';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

export const getFormSchema = (): FormSchema => {
    const { options, hasNextPage, isFetching, isLoading, loadNextPage } =
        sharedRiskExclusionsInfiniteController;

    return {
        risks: {
            type: 'combobox',
            label: t`Select risks`,
            loaderLabel: t`Loading results`,
            removeAllSelectedItemsLabel: t`Clean all`,
            getSearchEmptyState: () => t`No risks found`,
            isMultiSelect: true,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: loadNextPage,
            placeholder: t`Search by risk name or code...`,
        },
    };
};
