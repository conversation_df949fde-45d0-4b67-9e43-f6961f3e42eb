import { sharedPoliciesController } from '@controllers/policies';
import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPoliciesExternalPolicyBannerController } from './controllers/policies-external-policy-banner.controller';
import { getExternalPolicyProviderLabel } from './helpers/external-policy-labels.helper';

export const PoliciesExternalPolicyBanner = observer(
    (): React.JSX.Element | null => {
        const {
            showExternalPolicyBanner,
            externalPolicyConnection,
            hasBambooHrConnection,
        } = sharedPoliciesController;

        const { isVisible } = sharedPoliciesExternalPolicyBannerController;

        if (
            !showExternalPolicyBanner ||
            !externalPolicyConnection ||
            !isVisible
        ) {
            return null;
        }

        const providerLabel = getExternalPolicyProviderLabel(
            externalPolicyConnection.clientType,
        );

        const bannerTitle = hasBambooHrConnection
            ? t`You're using ${providerLabel} for policy content and acknowledgment`
            : t`You're using ${providerLabel} for policy content`;

        const handleClose = (): void => {
            sharedPoliciesExternalPolicyBannerController.handleClose();
        };

        return (
            <Banner
                displayMode="section"
                severity="primary"
                title={bannerTitle}
                closeButtonAriaLabel={t`Hide banner`}
                data-testid="PoliciesExternalPolicyBanner"
                data-id="policies-external-policy-banner"
                onClose={handleClose}
            />
        );
    },
);
