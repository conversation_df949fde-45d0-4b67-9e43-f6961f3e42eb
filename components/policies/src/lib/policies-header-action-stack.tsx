import { noop } from 'lodash-es';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension0x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';

export const PoliciesHeaderActionStack = (): React.JSX.Element => {
    return (
        <ActionStack
            data-id="policies-page-header-action-stack"
            gap={dimension0x}
            data-testid="PoliciesHeaderActionStack"
            actions={[
                {
                    actionType: 'button',
                    id: 'action-button',
                    typeProps: {
                        'data-id': 'action-button',
                        label: t({
                            message: 'Create custom policy',
                            comment:
                                'Button label for creating a custom policy',
                        }),
                        onClick: noop,
                        type: 'button',
                        level: 'secondary',
                    },
                },
            ]}
        />
    );
};
