import { sharedPoliciesController } from '@controllers/policies';
import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getExternalPolicyProviderLabel } from './helpers/external-policy-labels.helper';

export const PoliciesOutdatedBanner = observer((): React.JSX.Element | null => {
    const {
        hasPoliciesOutdated,
        showExternalPolicyBanner,
        externalPolicyConnection,
    } = sharedPoliciesController;

    if (
        !hasPoliciesOutdated ||
        !showExternalPolicyBanner ||
        !externalPolicyConnection
    ) {
        return null;
    }

    const providerLabel = getExternalPolicyProviderLabel(
        externalPolicyConnection.clientType,
    );

    return (
        <Banner
            displayMode="section"
            severity="warning"
            title={t`A policy was updated in ${providerLabel}`}
            body={t`Edit any policies marked with yellow to review the changes.`}
            data-testid="PoliciesOutdatedBanner"
            data-id="policies-outdated-banner"
        />
    );
});
