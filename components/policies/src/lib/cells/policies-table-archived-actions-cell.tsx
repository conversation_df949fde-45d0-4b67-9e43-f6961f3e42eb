import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { handleRestorePolicy } from '../helpers/handle-restore-policy.helper';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableArchivedActionsCell = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    return (
        <SchemaDropdown
            isIconOnly
            startIconName="Action"
            level="tertiary"
            colorScheme="neutral"
            data-testid="PoliciesTableArchivedActionsCell"
            data-id={String(row.original.id)}
            label={t({
                message: 'Actions',
                comment: 'Label for archived policy row actions dropdown',
            })}
            items={[
                {
                    id: 'policy-restore-button',
                    label: t({
                        message: 'Restore policy',
                        comment: 'Action to restore an archived policy',
                    }),
                    type: 'item',
                    startIconName: 'Sync',
                    onSelect: handleRestorePolicy(row),
                },
            ]}
        />
    );
};
