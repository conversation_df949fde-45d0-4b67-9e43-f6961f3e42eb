import { isEmpty, isNil } from 'lodash-es';
import {
    type OnSelect,
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { useNavigate } from '@remix-run/react';
import { handleArchivePolicy } from '../helpers/handle-archive-policy.helper';
import { handleDeletePolicy } from '../helpers/handle-delete-policy.helper';
import { handleEditPolicy } from '../helpers/handle-edit-policy.helper';
import { handleViewPolicyPdf } from '../helpers/handle-view-policy-pdf.helper';
import type { PoliciesTableCellProps } from '../types/policies.types';

class PolicyItemsModel {
    constructor(
        row: PoliciesTableCellProps['row'],
        navigate: (path: string) => void,
    ) {
        this.#row = row;
        this.#navigate = navigate;
    }

    #row: PoliciesTableCellProps['row'];
    #navigate: (path: string) => void;

    private get handleDeletePolicy(): OnSelect {
        return handleDeletePolicy(this.#row);
    }

    private get handleArchivePolicy(): OnSelect {
        return handleArchivePolicy(this.#row);
    }

    private get handleEditPolicy(): OnSelect {
        return handleEditPolicy(this.#row, this.#navigate);
    }

    private get handleViewPolicyPdf(): OnSelect {
        return handleViewPolicyPdf(this.#row);
    }

    private get isActivePolicy(): boolean {
        const { policyStatus, hasExternalPolicyConnection } =
            this.#row.original;

        return Boolean(
            policyStatus === 'ACTIVE' ||
                policyStatus === 'OUTDATED' ||
                (hasExternalPolicyConnection &&
                    policyStatus === 'UNACCEPTABLE'),
        );
    }

    private get hasSlas(): boolean {
        const {
            policyP3MatrixSLAs,
            policyWeekTimeFrameSLAs,
            policyGracePeriodSLAs,
        } = this.#row.original;

        return (
            !isEmpty(policyP3MatrixSLAs) ||
            !isEmpty(policyWeekTimeFrameSLAs) ||
            !isEmpty(policyGracePeriodSLAs)
        );
    }

    private get showViewPolicy(): boolean {
        const { version, canDownloadPolicies } = this.#row.original;

        return (
            this.isActivePolicy &&
            !isNil(version) &&
            canDownloadPolicies === true
        );
    }

    private get isDrataTemplate(): boolean {
        return !isNil(this.#row.original.templateId);
    }

    private get isStartBuilding(): boolean {
        return this.isDrataTemplate && isNil(this.#row.original.version);
    }

    private get isNewDrataTemplatePolicy(): boolean {
        const { version } = this.#row.original;

        return (
            this.isDrataTemplate &&
            !isNil(version) &&
            (version.formatted === '0' || version.formatted === '1')
        );
    }

    get items(): SchemaDropdownItems {
        const items: SchemaDropdownItems = [];
        const { version, isDraft } = this.#row.original;

        if (this.isActivePolicy) {
            if (this.isStartBuilding) {
                items.push({
                    id: 'policy-start-building-button',
                    label: 'Start building',
                    type: 'item',
                    onSelect: this.handleEditPolicy,
                });
            } else {
                items.push({
                    id: 'policy-edit-button',
                    label: 'Edit policy',
                    type: 'item',
                    onSelect: this.handleEditPolicy,
                });
            }
        }

        if (this.isActivePolicy && !this.hasSlas) {
            items.push({
                id: 'policies-archive-button',
                label: 'Archive policy',
                type: 'item',
                startIconName: 'Archive',
                onSelect: this.handleArchivePolicy,
            });
        }

        if (this.showViewPolicy) {
            const viewPolicyText =
                version.policyVersionStatus === 'PUBLISHED'
                    ? 'Download published version PDF'
                    : 'Download draft version PDF';

            items.push({
                id: 'policy-download-pdf-button',
                label: viewPolicyText,
                type: 'item',
                onSelect: this.handleViewPolicyPdf,
            });
        }

        if (isDraft && !this.isNewDrataTemplatePolicy) {
            items.push({
                id: 'policy-delete-button',
                label: 'Delete policy',
                type: 'item',
                startIconName: 'Trash',
                colorScheme: 'critical',
                onSelect: this.handleDeletePolicy,
            });
        }

        return items;
    }
}

export const PoliciesTableCellEditComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const navigate = useNavigate();
    const policyItemsModel = new PolicyItemsModel(row, navigate);

    return (
        <SchemaDropdown
            isIconOnly
            startIconName="Action"
            level="tertiary"
            colorScheme="neutral"
            data-testid="PoliciesTableCellEditComponent"
            data-id={String(row.original.id)}
            items={policyItemsModel.items}
            label={t({
                message: 'Actions',
                comment: 'Label for policy row actions dropdown',
            })}
        />
    );
};
