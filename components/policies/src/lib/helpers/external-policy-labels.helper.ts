import type { ClientTypeEnum } from '@globals/api-sdk/types';
/**
 * Maps external policy provider client types to user-friendly display names.
 * This helper provides a centralized mapping for external policy providers
 * to ensure consistent display names across the application.
 */
export function getExternalPolicyProviderLabel(
    clientType: ClientTypeEnum,
): string {
    switch (clientType) {
        case 'BAMBOO_HR':
        case 'MERGEDEV_BAMBOO_HR': {
            return 'BambooHR';
        }
        case 'CONFLUENCE': {
            return 'Confluence';
        }
        case 'NOTION': {
            return 'Notion';
        }
        default: {
            // Fallback to the original client type if no mapping is found
            return String(clientType);
        }
    }
}
