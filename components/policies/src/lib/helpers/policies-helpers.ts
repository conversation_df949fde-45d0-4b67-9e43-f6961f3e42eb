import { isNil } from 'lodash-es';
import type { policyOverviewMetrics } from '@controllers/policies';
import type {
    PolicyOverviewResponse,
    PolicyTableVersionResponseDto,
} from '@globals/api-sdk/types';
import { isDateWithinNextMonths } from '@helpers/date-time';
import {
    getStatusOverviewLabels,
    POLICY_RENEWAL_MONTHS_FOR_NEXT_DATE,
    RENEWAL_DATE_STATUS_METADATA,
} from '../policies.constants';
import type {
    PolicyOverviewMetadata,
    RenewalDate,
} from '../types/policies.types';

export const mapOverviewMetricMetadata = (
    overview: PolicyOverviewResponse,
): PolicyOverviewMetadata[] => {
    const keysToExclude: string[] = ['published', 'activePolicies'];
    const labels = getStatusOverviewLabels();

    const result: PolicyOverviewMetadata[] = Object.entries(overview)
        .filter(([key]) => !keysToExclude.includes(key))
        .map(([key, value]) => ({
            search: key as policyOverviewMetrics,
            label: labels[key as keyof typeof labels],
            value,
        }));

    return result;
};

export const mapRenewalDateAdaptor = (
    version: PolicyTableVersionResponseDto | null,
): RenewalDate => {
    const isPolicyNearRenewalDate =
        !isNil(version?.renewalDate) &&
        isDateWithinNextMonths(
            POLICY_RENEWAL_MONTHS_FOR_NEXT_DATE,
            new Date(version.renewalDate),
        );

    switch (true) {
        case isNil(version):
        case isNil(version?.renewalDate): {
            return RENEWAL_DATE_STATUS_METADATA.default;
        }
        case version?.hasExpiredRenewalDate: {
            return RENEWAL_DATE_STATUS_METADATA.expired;
        }
        case isPolicyNearRenewalDate: {
            return RENEWAL_DATE_STATUS_METADATA.nearRenewal;
        }
        default: {
            return RENEWAL_DATE_STATUS_METADATA.default;
        }
    }
};
