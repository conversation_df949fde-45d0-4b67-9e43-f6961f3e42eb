import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getPolicyStatusLabel = (
    policyVersionStatus: PolicyTableVersionResponseDto['policyVersionStatus'],
): {
    label: string;
    colorScheme: 'critical' | 'warning' | 'neutral' | 'success';
} => {
    switch (policyVersionStatus) {
        case 'PUBLISHED': {
            return {
                colorScheme: 'success',
                label: t({
                    message: 'Published',
                    comment: 'Policy status label for published policies',
                }),
            };
        }

        case 'NEEDS_APPROVAL': {
            return {
                colorScheme: 'critical',
                label: t({
                    message: 'Needs approval',
                    comment:
                        'Policy status label for policies needing approval',
                }),
            };
        }

        case 'DRAFT': {
            return {
                colorScheme: 'neutral',
                label: t({
                    message: 'New',
                    comment: 'Policy status label for new/draft policies',
                }),
            };
        }

        case 'APPROVED': {
            return {
                colorScheme: 'warning',
                label: t({
                    message: 'Approved',
                    comment: 'Policy status label for approved policies',
                }),
            };
        }

        default: {
            return {
                colorScheme: 'neutral',
                label: '-',
            };
        }
    }
};
