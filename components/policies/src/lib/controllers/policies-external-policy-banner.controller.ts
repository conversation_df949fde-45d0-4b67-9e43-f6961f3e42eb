import { logger } from '@globals/logger';
import { makeAutoObservable, runInAction } from '@globals/mobx';

const EXTERNAL_POLICY_BANNER_STORAGE_KEY =
    'multiverse-external-policy-banner-hidden';

/**
 * Controller for managing the external policy banner visibility state.
 * Follows the multiverse MVC pattern with MobX reactive state management.
 */
class PoliciesExternalPolicyBannerController {
    #isHidden = false;

    constructor() {
        makeAutoObservable(this);
        this.#initializeFromStorage();
    }

    /**
     * Initialize the banner visibility state from localStorage.
     */
    #initializeFromStorage(): void {
        try {
            const storedValue = localStorage.getItem(
                EXTERNAL_POLICY_BANNER_STORAGE_KEY,
            );

            this.#isHidden = storedValue === 'true';
        } catch (error) {
            // Gracefully handle localStorage errors (e.g., in private browsing mode)
            logger.warn({
                message: 'Failed to read banner state from localStorage',
                additionalInfo: { error },
            });
        }
    }

    /**
     * Whether the banner should be visible to the user.
     */
    get isVisible(): boolean {
        return !this.#isHidden;
    }

    /**
     * Hide the banner and persist the preference to localStorage.
     * Uses MobX action for proper state management.
     */
    handleClose(): void {
        runInAction(() => {
            this.#isHidden = true;
        });

        try {
            localStorage.setItem(EXTERNAL_POLICY_BANNER_STORAGE_KEY, 'true');
        } catch (error) {
            // Gracefully handle localStorage errors
            logger.warn({
                message: 'Failed to save banner state to localStorage',
                additionalInfo: { error },
            });
        }
    }

    /**
     * Reset the banner visibility (useful for testing or admin purposes).
     */
    resetBanner(): void {
        runInAction(() => {
            this.#isHidden = false;
        });

        try {
            localStorage.removeItem(EXTERNAL_POLICY_BANNER_STORAGE_KEY);
        } catch (error) {
            logger.warn({
                message: 'Failed to reset banner state in localStorage',
                additionalInfo: { error },
            });
        }
    }
}

export const sharedPoliciesExternalPolicyBannerController =
    new PoliciesExternalPolicyBannerController();
