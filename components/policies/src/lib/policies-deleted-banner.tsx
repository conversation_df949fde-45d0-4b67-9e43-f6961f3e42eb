import { sharedPoliciesController } from '@controllers/policies';
import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getExternalPolicyProviderLabel } from './helpers/external-policy-labels.helper';

export const PoliciesDeletedBanner = observer((): React.JSX.Element | null => {
    const {
        hasPoliciesDeleted,
        showExternalPolicyBanner,
        externalPolicyConnection,
    } = sharedPoliciesController;

    if (
        !hasPoliciesDeleted ||
        !showExternalPolicyBanner ||
        !externalPolicyConnection
    ) {
        return null;
    }

    const providerLabel = getExternalPolicyProviderLabel(
        externalPolicyConnection.clientType,
    );

    return (
        <Banner
            displayMode="section"
            severity="critical"
            title={t`A policy was deleted in ${providerLabel}`}
            body={t`Edit any policies marked in red. Import and associate a new file to these policies.`}
            data-testid="PoliciesDeletedBanner"
            data-id="policies-deleted-banner"
        />
    );
});
