import type { PolicyOverviewResponse } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type {
    PolicyOverviewMetadata,
    RenewalDate,
    RenewalDateStatus,
} from './types/policies.types';

export const RENEWAL_DATE_STATUS_METADATA: Record<
    RenewalDateStatus,
    RenewalDate
> = {
    expired: {
        name: 'WarningDiamond',
        colorScheme: 'critical',
        showIcon: true,
        allowBold: true,
    },
    nearRenewal: {
        name: 'WarningTriangle',
        colorScheme: 'warning',
        showIcon: true,
        allowBold: true,
    },
    default: {
        name: 'howSidebarMenu',
        colorScheme: 'neutral',
        showIcon: false,
        allowBold: false,
    },
};

export const getStatusOverviewLabels = () =>
    ({
        renewalUpcoming: t({
            message: 'Renewal required soon',
            comment: 'Label for policies requiring renewal soon',
        }),
        renewalPastDue: t({
            message: 'Renewal past due',
            comment: 'Label for policies with past due renewals',
        }),
        approvalNeedsApproval: t({
            message: 'Needs approval',
            comment: 'Label for policies needing approval',
        }),
        approvalReadyToPublish: t({
            message: 'Ready to publish',
            comment: 'Label for policies ready to publish',
        }),
    }) as const satisfies Record<
        keyof Omit<PolicyOverviewResponse, 'published' | 'activePolicies'>,
        string
    >;

export const getEmptyPoliciesOverviewMetadata =
    (): PolicyOverviewMetadata[] => {
        const labels = getStatusOverviewLabels();

        return [
            {
                label: labels.renewalUpcoming,
                value: 0,
                search: 'renewalUpcoming',
            },
            {
                label: labels.renewalPastDue,
                value: 0,
                search: 'renewalPastDue',
            },
            {
                label: labels.approvalNeedsApproval,
                value: 0,
                search: 'approvalNeedsApproval',
            },
            {
                label: labels.approvalReadyToPublish,
                value: 0,
                search: 'approvalReadyToPublish',
            },
        ];
    };

export const POLICY_RENEWAL_MONTHS_FOR_NEXT_DATE = 2;

export const FORM_ID = 'policy-add-form';
