import { size } from 'lodash-es';
import { panelController } from '@controllers/panel';
import { PanelBody, PanelControls } from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { action, observer } from '@globals/mobx';
import { sharedRiskPanelModel } from './model/risk-panel.model';
import { RiskRegisterPanelHeader } from './risk-panel-header.component';
import { AssessmentSection } from './sections/assessment-section';
import { CurrentTreatmentSection } from './sections/current-treatment-section';
import { DetailsSection } from './sections/details-section';
import { MitigatingControlsSection } from './sections/mitigating-controls-section';

const handleClosePanel = action(() => {
    panelController.closePanel();
});

export const RiskRegisterPanel = observer((): React.JSX.Element => {
    const { risksToUse, currentRiskIndex, loadPanelInfo } =
        sharedRiskPanelModel;

    return (
        <Stack
            data-testid="RiskRegisterPanel"
            direction="column"
            data-id="j9rODlfg"
        >
            <PanelControls
                closeButtonLabel="Close"
                data-Id="risk-panel-controls"
                pagination={{
                    currentItem: currentRiskIndex + 1,
                    onNextPageClick: () => {
                        if (currentRiskIndex === risksToUse.length - 1) {
                            return;
                        }
                        const nextRisk = risksToUse[currentRiskIndex + 1];

                        loadPanelInfo(nextRisk);
                    },
                    onPrevPageClick: () => {
                        if (currentRiskIndex === 0) {
                            return;
                        }
                        const prevRisk = risksToUse[currentRiskIndex - 1];

                        loadPanelInfo(prevRisk);
                    },
                    totalItems: size(risksToUse),
                }}
                onClose={handleClosePanel}
            />
            <RiskRegisterPanelHeader />
            <PanelBody data-Id="panel-body">
                <Stack gap="3xl" direction="column">
                    <DetailsSection />

                    <Divider />

                    <AssessmentSection />

                    <Divider />

                    <CurrentTreatmentSection />

                    <Divider />

                    <MitigatingControlsSection />
                </Stack>
            </PanelBody>
        </Stack>
    );
});
