import type { FrameworkDetailsController } from '@controllers/frameworks';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    FrameworkResponseDto,
    LevelImpactResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { type FormSchema, UniversalFormField } from '@ui/forms';
import { SelectFrameworkProfileModalContentBodyProfileImpact } from '../components/select-framework-profile-modal-content/select-framework-profile-modal-content-body-profile-impact';
import { getOscalProfileLabelById } from '../helpers/get-oscal-profile-label-by-id.helper';

export class SelectFrameworkProfileModel {
    frameworksDetailsController: FrameworkDetailsController;
    selectedProfile?: number;

    constructor(frameworksDetailsController: FrameworkDetailsController) {
        this.frameworksDetailsController = frameworksDetailsController;

        makeAutoObservable(this);
    }

    get isLoadingOptions(): boolean {
        return this.frameworksDetailsController.isLoadingProfile;
    }

    get profileOptions(): ListBoxItemData[] {
        return this.frameworksDetailsController.profiles.map((profile) => ({
            id: String(profile.oscalId),
            label: getOscalProfileLabelById(profile.oscalId),
            value: String(profile.id),
        }));
    }

    get formSchema(): FormSchema {
        return {
            profile: {
                type: 'custom',
                validateWithDefault: 'select',
                label:
                    this.frameworksDetailsController.frameworkDetails
                        ?.levelLabel ?? t`Baseline`,
                options: this.profileOptions,
                initialValue: this.profileOptions.find((option) => {
                    return (
                        option.id ===
                        this.frameworksDetailsController.selectedProfile
                    );
                }),
                render: action((fieldProps) => {
                    return (
                        <>
                            <UniversalFormField
                                __fromCustomRender
                                name={fieldProps.name}
                                formId={fieldProps.formId}
                                data-id={fieldProps['data-id']}
                            />

                            {fieldProps.value && fieldProps.isDirty && (
                                <SelectFrameworkProfileModalContentBodyProfileImpact
                                    selectFrameworkProfileModel={this}
                                    data-id="w9iUywfZ"
                                />
                            )}
                        </>
                    );
                }),
            },
        };
    }

    get modalTitle(): string {
        switch (this.frameworksDetailsController.frameworkDetails?.tag) {
            case 'FEDRAMP': {
                return t`Select FedRAMP baseline`;
            }
            case 'PCI4': {
                return t`Set the PCI DSS v4.0.1 SAQ type`;
            }
            case 'CIS8': {
                return t`Set the CIS 8.1 Implementation Groups`;
            }
            case 'HITRUST': {
                return t`Set the HITRUST Assessment Type`;
            }
            default: {
                return t`Unknown framework`;
            }
        }
    }

    get frameworkTag(): FrameworkResponseDto['tag'] {
        return this.frameworksDetailsController.frameworkDetails?.tag ?? 'NONE';
    }

    get profileImpact(): LevelImpactResponseDto | null {
        if (
            !this.frameworksDetailsController.frameworkDetails ||
            !this.selectedProfile
        ) {
            return null;
        }

        if (!this.frameworksDetailsController.isLoadingLevelImpact) {
            this.frameworksDetailsController.getLevelImpactQuery.load({
                path: {
                    frameworkId:
                        this.frameworksDetailsController.frameworkDetails.id,
                },
                query: {
                    profile: this.selectedProfile,
                },
            });
        }

        if (this.frameworksDetailsController.levelImpact) {
            return this.frameworksDetailsController.levelImpact;
        }

        return null;
    }

    get isLoadingProfileImpact(): boolean {
        return this.frameworksDetailsController.isLoadingLevelImpact;
    }
}
