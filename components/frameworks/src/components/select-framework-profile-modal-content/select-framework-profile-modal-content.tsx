import { isNil } from 'lodash-es';
import { use<PERSON>allback, useMemo, useRef } from 'react';
import {
    type FrameworkDetailsController,
    sharedFrameworkProfileMutationController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { useLingui } from '@globals/i18n/macro';
import { observer, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import type { FormValues } from '@ui/forms';
import { SELECT_FRAMEWORK_PROFILE_MODAL_ID } from '../../constants';
import { SelectFrameworkProfileModel } from '../../models/select-framework-profile.model';
import { SelectFrameworkProfileModalContentBody } from './select-framework-profile-modal-content-body';

interface Props {
    frameworksDetailsController: FrameworkDetailsController;
}

export const SelectFrameworkProfileModalContent = observer(
    ({ frameworksDetailsController }: Props): React.JSX.Element => {
        const { t } = useLingui();
        const navigate = useNavigate();
        const formRef = useRef<HTMLFormElement & { submitForm: () => void }>(
            null,
        );

        const { currentWorkspaceId } = sharedWorkspacesController;

        const selectFrameworkProfileModel = useMemo(
            () => new SelectFrameworkProfileModel(frameworksDetailsController),
            [frameworksDetailsController],
        );

        const { modalTitle } = useMemo(
            () => selectFrameworkProfileModel,
            [selectFrameworkProfileModel],
        );

        const handleClose = useCallback(() => {
            modalController.closeModal(SELECT_FRAMEWORK_PROFILE_MODAL_ID);
        }, []);

        const handleSave = useCallback(() => {
            formRef.current?.submitForm();
        }, []);

        const handleSubmit = useCallback(
            async (formValues: FormValues) => {
                const profileId =
                    // This is safe as long as the form validation is applied.
                    (formValues as { profile: { value: string } }).profile
                        .value;

                await when(
                    () => !isNil(frameworksDetailsController.frameworkDetails),
                );

                const frameworkId =
                    frameworksDetailsController.frameworkDetails?.id;

                // This should be a given at this point, but TypeScript doesn't know that
                if (!frameworkId) {
                    return;
                }

                sharedFrameworkProfileMutationController.updateFrameworkProfile(
                    frameworkId,
                    Number(profileId),
                );

                when(
                    () =>
                        !sharedFrameworkProfileMutationController.isLoading &&
                        !sharedFrameworkProfileMutationController.hasError,
                    () => {
                        handleClose();

                        navigate(
                            `/workspaces/${currentWorkspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`,
                        );
                    },
                );
            },
            [
                currentWorkspaceId,
                frameworksDetailsController.frameworkDetails,
                handleClose,
                navigate,
            ],
        );

        const { isLoading } = sharedFrameworkProfileMutationController;

        const actions = useMemo<ButtonProps[]>(
            () => [
                {
                    label: t`Cancel`,
                    level: 'secondary',
                    onClick: handleClose,
                },
                {
                    label: t`Save`,
                    level: 'primary',
                    colorScheme: 'primary',
                    onClick: handleSave,
                    isLoading,
                },
            ],
            [handleClose, handleSave, isLoading, t],
        );

        return (
            <>
                <Modal.Header
                    title={modalTitle}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleClose}
                />

                <Modal.Body>
                    <Stack
                        gap="6x"
                        direction="column"
                        data-testid="SelectFrameworkProfileModalContent"
                        data-id="goWnAw83"
                    >
                        <SelectFrameworkProfileModalContentBody
                            formRef={formRef}
                            handleSubmit={handleSubmit}
                            selectFrameworkProfileModel={
                                selectFrameworkProfileModel
                            }
                        />
                    </Stack>
                </Modal.Body>

                <Modal.Footer rightActionStack={actions} />
            </>
        );
    },
);
