import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { Form } from '@ui/forms';
import type { LazyBodyProps } from './select-framework-profile-modal-content-body';

export const SelectFrameworkProfileModalContentBodyHitrust = ({
    isLoading,
    formSchema,
    formRef,
    handleSubmit,
    formId,
}: LazyBodyProps): React.JSX.Element => {
    return (
        <Stack
            gap="2x"
            direction="column"
            data-testid="SelectFrameworkProfileModalContentBodyHitrust"
            data-id="uBOyCVhn"
        >
            <Text size="200">
                <Trans>
                    Setting the HITRUST Assessment Type will automatically mark
                    inapplicable requirements as out-of-scope. You can manually
                    mark requirements in scope or you can change the Assessment
                    Type at any time.
                </Trans>
            </Text>

            {isLoading ? (
                <Skeleton />
            ) : (
                <Form
                    hasExternalSubmitButton
                    data-id="Jxq_m6Su"
                    formId={formId}
                    ref={formRef}
                    schema={formSchema}
                    onSubmit={handleSubmit}
                />
            )}
        </Stack>
    );
};
