import { lazy, type LazyExoticComponent, Suspense, useMemo } from 'react';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { FormSchema, FormValues } from '@ui/forms';
import { SELECT_FRAMEWORK_PROFILE_FORM_ID } from '../../constants';
import type { SelectFrameworkProfileModel } from '../../models/select-framework-profile.model';

const getBodyLazyComponent = (
    frameworkTag: FrameworkResponseDto['tag'],
): LazyExoticComponent<(props: LazyBodyProps) => React.JSX.Element | null> =>
    lazy(() => {
        switch (frameworkTag) {
            case 'FEDRAMP': {
                return import(
                    './select-framework-profile-modal-content-body-fedramp'
                ).then((mod) => ({
                    default: mod.SelectFrameworkProfileModalContentBodyFedramp,
                }));
            }
            case 'PCI4': {
                return import(
                    './select-framework-profile-modal-content-body-pci'
                ).then((mod) => ({
                    default: mod.SelectFrameworkProfileModalContentBodyPci,
                }));
            }
            case 'CIS8': {
                return import(
                    './select-framework-profile-modal-content-body-cis8'
                ).then((mod) => ({
                    default: mod.SelectFrameworkProfileModalContentBodyCis8,
                }));
            }
            case 'HITRUST': {
                return import(
                    './select-framework-profile-modal-content-body-hitrust'
                ).then((mod) => ({
                    default: mod.SelectFrameworkProfileModalContentBodyHitrust,
                }));
            }
            default: {
                console.warn(`Not implemented frameworkTag: ${frameworkTag}\n`);

                return Promise.resolve({
                    default: () => (
                        <Trans data-id="ayxyyU8-">Not implemented</Trans>
                    ),
                });
            }
        }
    });

export interface LazyBodyProps {
    isLoading: boolean;
    formSchema: FormSchema;
    formRef: React.RefObject<HTMLFormElement>;
    handleSubmit: (formValues: FormValues) => void;
    formId: string;
}

interface Props {
    selectFrameworkProfileModel: SelectFrameworkProfileModel;
    formRef: React.RefObject<HTMLFormElement>;
    handleSubmit: (formValues: FormValues) => void;
}

export const SelectFrameworkProfileModalContentBody = observer(
    ({
        selectFrameworkProfileModel,
        formRef,
        handleSubmit,
    }: Props): React.JSX.Element => {
        const LazyBodyComponent = useMemo(
            () =>
                getBodyLazyComponent(selectFrameworkProfileModel.frameworkTag),
            [selectFrameworkProfileModel.frameworkTag],
        );

        return (
            <Suspense data-id="0g2zjnNa">
                <LazyBodyComponent
                    isLoading={selectFrameworkProfileModel.isLoadingOptions}
                    formSchema={selectFrameworkProfileModel.formSchema}
                    formRef={formRef}
                    handleSubmit={handleSubmit}
                    formId={SELECT_FRAMEWORK_PROFILE_FORM_ID}
                />
            </Suspense>
        );
    },
);
