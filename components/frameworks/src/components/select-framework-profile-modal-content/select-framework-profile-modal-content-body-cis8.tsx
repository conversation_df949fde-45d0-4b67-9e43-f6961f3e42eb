import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { Form } from '@ui/forms';
import type { LazyBodyProps } from './select-framework-profile-modal-content-body';

export const SelectFrameworkProfileModalContentBodyCis8 = ({
    isLoading,
    formSchema,
    formRef,
    handleSubmit,
    formId,
}: LazyBodyProps): React.JSX.Element => {
    return (
        <Stack
            gap="2x"
            direction="column"
            data-testid="SelectFrameworkProfileModalContentBodyCis8"
            data-id="2I-PX4op"
        >
            <Text size="200">
                <Trans>
                    Setting the CIS 8.1 Implementation Groups will automatically
                    mark inapplicable requirements as out-of-scope. You can
                    manually mark requirements in scope or you can change the
                    Implementation Groups at any time.
                </Trans>
            </Text>

            {isLoading ? (
                <Skeleton />
            ) : (
                <Form
                    hasExternalSubmitButton
                    data-id="YuyK466r"
                    formId={formId}
                    ref={formRef}
                    schema={formSchema}
                    onSubmit={handleSubmit}
                />
            )}
        </Stack>
    );
};
