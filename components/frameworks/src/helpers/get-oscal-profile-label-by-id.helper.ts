export function getOscalProfileLabelById(profileId: string): string {
    switch (profileId) {
        case '1b4bf092-74f0-4be0-b257-51790a4a368f': {
            return 'LI-SaaS';
        }
        case '507c50c8-53d2-44a2-a1b6-3c3264c4bb25': {
            return 'Low';
        }
        case '3476aeb6-39ee-494d-b3ea-37337aa2b265': {
            return 'Moderate';
        }
        case 'b0ff31fe-ae4b-43a1-ba03-3508a69c6356': {
            return 'High';
        }
        case 'cf43e706-c439-4888-a8dc-410661e43e09': {
            return 'None';
        }
        case '3947c6c3-dce7-4b76-9ca6-cbc81fb446ab': {
            return 'A';
        }
        case '9c25f032-af52-4f92-9591-2851f5e6ab26': {
            return 'A-EP';
        }
        case '9527c7e7-8175-4a6f-a01a-773c71fde3a0': {
            return 'B';
        }
        case 'dfa5df90-d696-49c3-9ca4-3bdf01b46998': {
            return 'B-IP';
        }
        case '1aab5488-f383-4a1a-8bf4-0f3f81ef92fd': {
            return 'C';
        }
        case '2b9f09aa-c36f-4eec-82ef-2ee3bd7cad1f': {
            return 'C-VT';
        }
        case 'deffbd5c-98e7-405b-8d93-076e825d3364': {
            return 'D for Merchants';
        }
        case '2468a579-6fb7-420b-b444-f62c0c5d3292': {
            return 'D for Service Providers';
        }
        case '29cef565-20ca-4535-a20a-2cbf2b6cfc72': {
            return 'P2PE';
        }
        case '9e560fe2-51cd-4564-83c3-5b7eb03b5ddc': {
            return 'SPoC';
        }
        case '48f1b67c-3ffb-4cd7-9cc7-6e084a2efdc1': {
            return 'IG1';
        }
        case 'ae237302-5373-4343-9380-a2a15850dc8d': {
            return 'IG2';
        }
        case '33592fb8-9a7f-4774-bbbd-b8795ec36f15': {
            return 'IG3';
        }
        case 'e456fe6a-b20d-4893-a670-73c25c7e4340': {
            return 'e1';
        }
        case 'd69566a0-8b25-43f8-a254-94a63ed7acd2': {
            return 'i1';
        }
        case 'c7f492ac-7ebe-4f66-92be-78e9ecdda29d': {
            return 'r2';
        }
        default: {
            return '';
        }
    }
}
