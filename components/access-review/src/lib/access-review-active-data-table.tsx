import { useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { accessReviewApplicationToDatatableAdaptor } from './adaptors/access-review-data-table.adaptor';
import { getColumns } from './constants/access-review-active-data-table-columns.constants';
import { handleAccessReviewRowNavigation } from './helpers/active-data-table.helper';
import { useBuildAccessReviewApplicationActions } from './hooks/use-build-access-review-application-actions.hook';

export const AccessReviewActiveDataTable = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { isLoading, applicationsWithProviderInfo, totalApplications } =
        sharedActiveAccessReviewPeriodsController;
    const { currentWorkspaceId: workspaceId } = sharedWorkspacesController;

    const buildAccessReviewTableActions =
        useBuildAccessReviewApplicationActions();

    const data = useMemo(
        () =>
            accessReviewApplicationToDatatableAdaptor(
                applicationsWithProviderInfo,
                buildAccessReviewTableActions,
            ),
        [applicationsWithProviderInfo, buildAccessReviewTableActions],
    );

    return (
        <AppDatatable
            isLoading={isLoading}
            tableId="datatable-governance-access-review-active"
            columns={getColumns()}
            total={totalApplications}
            data-testid="AccessReviewActiveDataTable"
            data-id="u1Q5xT_N"
            data={data}
            tableSearchProps={{
                hideSearch: true,
            }}
            onRowClick={({ row }) => {
                handleAccessReviewRowNavigation(
                    row.id,
                    row.reviewPeriodId,
                    row.status,
                    workspaceId,
                    navigate,
                );
            }}
        />
    );
});
