/**
 * Handles navigation logic for access review application rows.
 *
 * @param id - The application ID.
 * @param reviewPeriodId - The review period ID.
 * @param status - The application status.
 * @param workspaceId - The current workspace ID.
 * @param navigate - The navigation function.
 * @returns Void - Early returns if navigation should be prevented.
 */
export function handleAccessReviewRowNavigation(
    id: number,
    reviewPeriodId: number,
    status: string,
    workspaceId: number | null,
    navigate: (path: string) => void,
): void {
    // Prevent navigation if workspaceId is not available
    if (workspaceId === null) {
        return;
    }

    // Navigate based on application status
    if (status === 'COMPLETED') {
        navigate(
            `/workspaces/${workspaceId}/governance/access-review/completed/period/${reviewPeriodId}/applications/${id}`,
        );
    } else {
        navigate(
            `/workspaces/${workspaceId}/governance/access-review/active/${id}/period/${reviewPeriodId}/overview`,
        );
    }
}
