import { modalController } from '@controllers/modal';
import { sharedPoliciesLibraryInfiniteListController } from '@controllers/policies';
import { action } from '@globals/mobx';
import { MAP_POLICIES_MODAL_ID } from '@models/controls';
import { MapPoliciesModal } from './map-policies-modal.component';

export const openMapPoliciesModal = (): void => {
    action(() => {
        sharedPoliciesLibraryInfiniteListController.load();
    })();

    modalController.openModal({
        id: MAP_POLICIES_MODAL_ID,
        content: () => <MapPoliciesModal data-id="MapPoliciesModal" />,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
