import { useMemo } from 'react';
import {
    PoliciesDeletedBanner,
    PoliciesExternalPolicyBanner,
    PoliciesOutdatedBanner,
    PoliciesRenewalDateBanner,
} from '@components/policies';
import { sharedPoliciesController } from '@controllers/policies';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { useNavigate } from '@remix-run/react';
import { policiesAdaptor } from './policies-adaptors';
import {
    createPoliciesEmptyStateProps,
    getPoliciesArchivedColumns,
} from './policies-view.helpers';

export const PoliciesArchiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        archivedPoliciesList,
        loadArchivedPolicies,
        isArchivedPoliciesLoading,
        archivedPoliciesTotal,
        currentParams,
        overviewFilter,
    } = sharedPoliciesController;

    const archivedPolicies = policiesAdaptor(archivedPoliciesList);

    const { externalPolicyConnection, hasBambooHrConnection, filters } =
        sharedPoliciesPageHeaderModel;

    const columns = getPoliciesArchivedColumns(
        hasBambooHrConnection,
        externalPolicyConnection,
    );

    const navigateToPolicyDetail = (policyId: number) => {
        navigate(
            `/workspaces/1/governance/policies/builder/${policyId}/overview`,
        );
    };

    // Memoized empty state props to prevent unnecessary re-renders
    const emptyStateProps = useMemo(
        () => createPoliciesEmptyStateProps(currentParams, 'archived'),
        [currentParams],
    );

    return (
        <Grid gap="lg" data-testid="PoliciesArchiveView" data-id="mGhO8JRX">
            <PoliciesExternalPolicyBanner />
            <PoliciesOutdatedBanner />
            <PoliciesDeletedBanner />
            <PoliciesRenewalDateBanner />
            <Datatable
                key={`datatable-policies-${overviewFilter || 'none'}`}
                isLoading={isArchivedPoliciesLoading}
                tableId="datatable-policies"
                data-id="datatable-policies"
                data={archivedPolicies}
                columns={columns}
                total={archivedPoliciesTotal}
                filterProps={filters}
                tableActions={[]}
                emptyStateProps={emptyStateProps}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t({
                        message: 'Search by name...',
                        comment: 'Placeholder text for policy name search',
                    }),
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t({
                            message: 'Pin filters to page',
                            comment: 'Label for pinning filters to page',
                        }),
                        toggleUnpinnedLabel: t({
                            message: 'Move filters to dropdown',
                            comment: 'Label for moving filters to dropdown',
                        }),
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadArchivedPolicies}
                onRowClick={({ row }) => {
                    navigateToPolicyDetail(row.id);
                }}
            />
        </Grid>
    );
});
