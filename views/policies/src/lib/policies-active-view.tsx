import { isNil } from 'lodash-es';
import { useMemo } from 'react';
import {
    getEmptyPoliciesOverviewMetadata,
    mapOverviewMetricMetadata,
    PoliciesDeletedBanner,
    PoliciesExternalPolicyBanner,
    PoliciesOutdatedBanner,
    PoliciesOverviewMetricComponent,
    PoliciesRenewalDateBanner,
    type PolicyOverviewMetadata,
} from '@components/policies';
import { sharedPoliciesController } from '@controllers/policies';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { useNavigate } from '@remix-run/react';
import { policiesAdaptor } from './policies-adaptors';
import {
    createPoliciesEmptyStateProps,
    getPoliciesActiveColumns,
    getPoliciesTableActions,
} from './policies-view.helpers';

const POLICY_DOWNLOADS_DISABLED_DOMAINS = ['teamhgs.com'];

export const PoliciesActiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        activePoliciesList,
        loadActivePolicies,
        isActivePoliciesLoading,
        activePoliciesTotal,
        overviewData,
        hasPublishedPolicies,
        currentParams,
        isDownloadingAllPolicies,
        downloadAllPoliciesFile,
        overviewFilter,
    } = sharedPoliciesController;

    const { externalPolicyConnection, hasBambooHrConnection, filters } =
        sharedPoliciesPageHeaderModel;

    const { company } = sharedCurrentCompanyController;

    const mappedOverviewData: PolicyOverviewMetadata[] = isNil(overviewData)
        ? getEmptyPoliciesOverviewMetadata()
        : mapOverviewMetricMetadata(overviewData);

    const domainHasPolicyDownloadsDisabled =
        POLICY_DOWNLOADS_DISABLED_DOMAINS.includes(company?.domain ?? '');

    const canDownloadPolicies =
        hasPublishedPolicies && !domainHasPolicyDownloadsDisabled;

    const activePolicies = policiesAdaptor(
        activePoliciesList,
        externalPolicyConnection,
        canDownloadPolicies,
    );

    const columns = getPoliciesActiveColumns(
        hasBambooHrConnection,
        externalPolicyConnection,
    );

    const handleDownloadAllPolicies = (): void => {
        downloadAllPoliciesFile();
    };

    const tableActions = getPoliciesTableActions(
        canDownloadPolicies,
        hasPublishedPolicies,
        isDownloadingAllPolicies,
        handleDownloadAllPolicies,
    );

    const navigateToPolicyDetail = (policyId: number) => {
        navigate(
            `/workspaces/1/governance/policies/builder/${policyId}/overview`,
        );
    };

    // Memoized empty state props to prevent unnecessary re-renders
    const emptyStateProps = useMemo(
        () => createPoliciesEmptyStateProps(currentParams, 'active'),
        [currentParams],
    );

    return (
        <Grid gap="lg" data-testid="PoliciesActiveView" data-id="CG_3uqvf">
            <PoliciesExternalPolicyBanner />
            <PoliciesOutdatedBanner />
            <PoliciesDeletedBanner />
            <PoliciesRenewalDateBanner />
            <Stack gap="4x">
                {mappedOverviewData.map((overview) => (
                    <PoliciesOverviewMetricComponent
                        key={overview.label}
                        data-id={overview.label}
                        value={overview.value}
                        label={overview.label}
                        search={overview.search}
                    />
                ))}
            </Stack>
            <Datatable
                key={`datatable-active-policies-${overviewFilter || 'none'}`}
                isLoading={isActivePoliciesLoading}
                tableId="datatable-active-policies"
                data-id="datatable-active-policies"
                data={activePolicies}
                columns={columns}
                total={activePoliciesTotal}
                filterProps={filters}
                tableActions={tableActions}
                emptyStateProps={emptyStateProps}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t({
                        message: 'Search by name...',
                        comment: 'Placeholder text for policy name search',
                    }),
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t({
                            message: 'Pin filters to page',
                            comment: 'Label for pinning filters to page',
                        }),
                        toggleUnpinnedLabel: t({
                            message: 'Move filters to dropdown',
                            comment: 'Label for moving filters to dropdown',
                        }),
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadActivePolicies}
                onRowClick={({ row }) => {
                    navigateToPolicyDetail(row.id);
                }}
            />
        </Grid>
    );
});
