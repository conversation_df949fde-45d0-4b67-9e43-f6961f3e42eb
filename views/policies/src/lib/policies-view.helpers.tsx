import { isEmpty, isNil, isObject } from 'lodash-es';
import type { ComponentProps } from 'react';
import {
    type PoliciesData,
    PoliciesTableApprovedDateCell,
    PoliciesTableArchivedActionsCell,
    PoliciesTableCellEditComponent,
    PoliciesTableCellExternalSourceComponent,
    PoliciesTableCellOwnerComponent,
    PoliciesTableCellPersonnelComponent,
    PoliciesTableCellSLAComponent,
    PoliciesTableCellStatusComponent,
    PoliciesTableCellTextComponent,
    PoliciesTableCellVersionComponent,
    PoliciesTablePublishedDateCell,
    PoliciesTableRenewalDateCell,
} from '@components/policies';
import type {
    Datatable,
    DatatableProps,
    FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getPoliciesActiveColumns = (
    hasBambooHrConnection: boolean,
    externalPolicyConnection: boolean,
): DatatableProps<PoliciesData>['columns'] => {
    return [
        {
            id: 'ACTIONS',
            header: '',
            enableSorting: false,
            accessorKey: 'version',
            minSize: 40,
            maxSize: 60,
            size: 50,
            isActionColumn: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: PoliciesTableCellEditComponent,
        },
        {
            id: 'NAME',
            header: t({
                message: 'Name',
                comment: 'Column header for policy name',
            }),
            enableSorting: true,
            accessorKey: 'name',
        },
        {
            id: 'VERSION',
            header: t({
                message: 'Version',
                comment: 'Column header for policy version',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellVersionComponent,
        },
        {
            id: 'STATUS',
            header: t({
                message: 'Status',
                comment: 'Column header for policy status',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellStatusComponent,
        },
        {
            id: 'CREATED',
            header: t({
                message: 'Created on',
                comment: 'Column header for policy creation date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            minSize: 170,
            cell: PoliciesTableCellTextComponent,
        },
        {
            id: 'APPROVED_DATE',
            header: hasBambooHrConnection
                ? t({
                      message: 'Approved in Bamboo HR',
                      comment: 'Column header for Bamboo HR approval date',
                  })
                : t({
                      message: 'Approved on',
                      comment: 'Column header for policy approval date',
                  }),
            minSize: 170,
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableApprovedDateCell,
        },
        {
            id: 'PUBLISHED_DATE',
            header: t({
                message: 'Published on',
                comment: 'Column header for policy publication date',
            }),
            minSize: 170,
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTablePublishedDateCell,
        },
        {
            id: 'RENEWAL_DATE',
            header: t({
                message: 'Renewal date',
                comment: 'Column header for policy renewal date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableRenewalDateCell,
        },
        ...(externalPolicyConnection
            ? [
                  {
                      id: 'EXTERNAL_SOURCE',
                      header: t({
                          message: 'External source',
                          comment: 'Column header for external policy source',
                      }),
                      enableSorting: false,
                      accessorKey: 'name',
                      accessorFn: () => false,
                      enableHiding: true,
                      cell: PoliciesTableCellExternalSourceComponent,
                  },
              ]
            : []),
        {
            id: 'OWNER',
            header: t({
                message: 'Owner',
                comment: 'Column header for policy owner',
            }),
            enableSorting: false,
            accessorKey: 'currentOwner',
            cell: PoliciesTableCellOwnerComponent,
        },
        {
            id: 'GROUPS',
            header: t({
                message: 'Assignment',
                comment: 'Column header for policy assignment',
            }),
            enableSorting: false,
            accessorKey: 'assignedTo',
            cell: PoliciesTableCellPersonnelComponent,
        },
        {
            id: 'SLA',
            header: t({
                message: 'SLA',
                comment: 'Column header for policy SLA',
            }),
            enableSorting: true,
            accessorKey: 'version',
            minSize: 150,
            cell: PoliciesTableCellSLAComponent,
        },
    ] as const satisfies DatatableProps<PoliciesData>['columns'];
};

export const getPoliciesArchivedColumns = (
    hasBambooHrConnection: boolean,
    externalPolicyConnection: boolean,
): DatatableProps<PoliciesData>['columns'] => {
    return [
        {
            id: 'ACTIONS',
            header: '',
            enableSorting: false,
            accessorKey: 'version',
            isActionColumn: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            minSize: 40,
            cell: PoliciesTableArchivedActionsCell,
        },
        {
            id: 'NAME',
            header: t({
                message: 'Name',
                comment: 'Column header for policy name',
            }),
            enableSorting: true,
            accessorKey: 'name',
        },
        {
            id: 'REPLACED_BY',
            header: t({
                message: 'Replaced by',
                comment: 'Column header for policy replacement',
            }),
            enableSorting: false,
            accessorKey: 'replacedBy',
        },
        {
            id: 'VERSION',
            header: t({
                message: 'Version',
                comment: 'Column header for policy version',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellVersionComponent,
        },
        {
            id: 'STATUS',
            header: t({
                message: 'Status',
                comment: 'Column header for policy status',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellStatusComponent,
        },
        {
            id: 'CREATED',
            header: t({
                message: 'Created on',
                comment: 'Column header for policy creation date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableCellTextComponent,
        },
        {
            id: 'APPROVED_DATE',
            header: hasBambooHrConnection
                ? t({
                      message: 'Approved in Bamboo HR',
                      comment: 'Column header for Bamboo HR approval date',
                  })
                : t({
                      message: 'Approved on',
                      comment: 'Column header for policy approval date',
                  }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableApprovedDateCell,
        },
        {
            id: 'PUBLISHED_DATE',
            header: t({
                message: 'Published on',
                comment: 'Column header for policy publication date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTablePublishedDateCell,
        },
        {
            id: 'RENEWAL_DATE',
            header: t({
                message: 'Renewal date',
                comment: 'Column header for policy renewal date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableRenewalDateCell,
        },
        ...(externalPolicyConnection
            ? [
                  {
                      id: 'EXTERNAL_SOURCE',
                      header: t({
                          message: 'External source',
                          comment: 'Column header for external policy source',
                      }),
                      enableSorting: false,
                      accessorKey: 'name',
                      accessorFn: () => false,
                      enableHiding: true,
                      cell: PoliciesTableCellExternalSourceComponent,
                  },
              ]
            : []),
        {
            id: 'OWNER',
            header: t({
                message: 'Owner',
                comment: 'Column header for policy owner',
            }),
            enableSorting: false,
            accessorKey: 'currentOwner',
            cell: PoliciesTableCellOwnerComponent,
        },
        {
            id: 'GROUPS',
            header: t({
                message: 'Assignment',
                comment: 'Column header for policy assignment',
            }),
            enableSorting: false,
            accessorKey: 'assignedTo',
            cell: PoliciesTableCellPersonnelComponent,
        },
        {
            id: 'SLA',
            header: t({
                message: 'SLA',
                comment: 'Column header for policy SLA',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableCellSLAComponent,
        },
        {
            id: 'ACTIONS',
            header: '',
            enableSorting: false,
            accessorKey: 'version',
            minSize: 40,
            maxSize: 60,
            size: 50,
            isActionColumn: false,
            cell: PoliciesTableArchivedActionsCell,
        },
    ];
};

export const getPoliciesTableActions = (
    canDownloadPolicies: boolean,
    hasPublishedPolicies: boolean,
    isDownloading: boolean,
    onDownloadAllPolicies: () => void,
): ComponentProps<typeof Datatable>['tableActions'] => {
    // Only show the download button when it's actually usable
    // This provides better UX than showing a disabled button
    if (!canDownloadPolicies || !hasPublishedPolicies) {
        return [];
    }

    return [
        {
            actionType: 'button',
            id: 'download-all-policies-button',
            typeProps: {
                label: t({
                    message: 'Download all',
                    comment: 'Button label to download all policies',
                }),
                colorScheme: 'neutral',
                level: 'secondary',
                startIconName: 'Download',
                isLoading: isDownloading,
                a11yLoadingLabel: t({
                    message: 'Downloading all policies',
                    comment: 'Loading label when downloading all policies',
                }),
                onClick: onDownloadAllPolicies,
            },
        },
    ];
};

/**
 * Helper function to determine if there are active filters or search.
 * Used to show different empty state messages for "no data" vs "no search results".
 */
export const hasActiveFiltersOrSearch = (
    currentParams: FetchDataResponseParams | null,
): boolean => {
    if (!currentParams?.globalFilter) {
        return false;
    }

    const { search, filters } = currentParams.globalFilter;

    // Check if there's an active search
    if (search && search.trim() !== '') {
        return true;
    }

    // Check if there are active filters
    if (isObject(filters)) {
        const filterValues = Object.values(filters);

        return filterValues.some((filterState) => {
            if (!isObject(filterState)) {
                return false;
            }

            // Type assertion since we know the structure from GlobalFilterState
            const typedFilterState = filterState as {
                value?: unknown;
                filterType: string;
            };
            const { value } = typedFilterState;

            if (Array.isArray(value)) {
                return !isEmpty(value);
            }

            return !isNil(value) && value !== '';
        });
    }

    return false;
};

/**
 * Creates dynamic empty state props based on whether there are active filters/search.
 * Shows different messages for "no data" vs "no search results" scenarios.
 */
export const createPoliciesEmptyStateProps = (
    currentParams: FetchDataResponseParams | null,
    type: 'active' | 'archived',
): {
    title: string;
    description: string;
    illustrationName: 'Files';
} => {
    const hasFiltersOrSearch = hasActiveFiltersOrSearch(currentParams);

    if (hasFiltersOrSearch) {
        // User has active search/filters but no results
        return {
            title: t({
                message: 'No results found',
                comment:
                    'Empty state title when no policies match search/filters',
            }),
            description: t({
                message: 'Try adjusting your search terms or filters.',
                comment:
                    'Empty state description when search/filters return no results',
            }),
            illustrationName: 'Files' as const,
        };
    }

    // No search/filters active - show first-time user message
    if (type === 'active') {
        return {
            title: t({
                message: 'No active policies',
                comment:
                    'Empty state title when there are no active policies at all',
            }),
            description: t({
                message:
                    'Create your first policy to get started with governance.',
                comment:
                    'Empty state description encouraging first policy creation',
            }),
            illustrationName: 'Files' as const,
        };
    }

    return {
        title: t({
            message: 'No archived policies',
            comment:
                'Empty state title when there are no archived policies at all',
        }),
        description: t({
            message: 'Archived and replaced policies will appear here.',
            comment:
                'Empty state description explaining what archived policies are',
        }),
        illustrationName: 'Files' as const,
    };
};
