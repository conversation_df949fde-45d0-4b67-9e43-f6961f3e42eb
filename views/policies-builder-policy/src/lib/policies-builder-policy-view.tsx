import { isNil } from 'lodash-es';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { PoliciesEditorComponent } from '@components/policies-editor';
import { sharedPolicyFilesController } from '@controllers/policies';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { observer } from '@globals/mobx';

export const PoliciesBuilderPolicyView = observer((): React.JSX.Element => {
    const { signedUrlFile } = sharedPolicyFilesController;
    const { isPolicyLoading, policy } = sharedPolicyBuilderController;

    const IS_EDIT_MODE = !isNil(policy?.latestPolicyVersion?.html);

    if (isPolicyLoading) {
        return (
            <Stack
                direction="column"
                align="center"
                gap="6x"
                p="8x"
                data-testid="PoliciesBuilderPolicyViewLoader"
                data-id="5DYP_Z0x"
            >
                <Loader isSpinnerOnly label="Loading" />
            </Stack>
        );
    }

    if (isNil(policy?.latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <>
            {IS_EDIT_MODE ? (
                <PoliciesEditorComponent />
            ) : (
                <PdfViewer
                    src={signedUrlFile ?? ''}
                    label={policy.name}
                    data-id="EUID_Z0b"
                />
            )}
        </>
    );
});
