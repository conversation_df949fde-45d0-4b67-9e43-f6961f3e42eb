import type { MouseEvent } from 'react';
import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const OptionsTableActionCell = observer(
    ({
        row,
    }: {
        row: { original: AuditHubEvidenceResponseDto };
    }): React.JSX.Element => {
        const { downloadEvidence } = sharedAuditHubEvidenceController;

        return (
            <SchemaDropdown
                isIconOnly
                size="sm"
                startIconName="HorizontalMenu"
                level="tertiary"
                label={t`Horizontal menu`}
                colorScheme="neutral"
                data-id="6GvBWQ_R"
                data-testid="OptionsTableActionCell"
                items={[
                    {
                        id: 'download-evidence-option',
                        label: t`Download`,
                        type: 'item',
                        onClick: (event: MouseEvent) => {
                            event.stopPropagation(); // Prevent row click event
                            downloadEvidence(row.original);
                        },
                    },
                ]}
            />
        );
    },
);
