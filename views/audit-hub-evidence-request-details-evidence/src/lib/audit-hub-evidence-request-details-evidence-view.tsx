import { AppDatatable } from '@components/app-datatable';
import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import {
    EVIDENCE_REQUEST_DETAILS_PAGINATION_OPTIONS,
    getEvidenceRequestDetailsColumns,
} from './constants/evidence-columns.constant';
import { getEvidenceRequestDetailsFilters } from './helpers/evidence-filters.helper';
import { sharedEvidenceBulkActionsModel } from './models/evidence-bulk-actions.model';

export const EvidenceRequestDetailsEvidenceView = observer((): JSX.Element => {
    const navigate = useNavigate();
    const {
        auditCustomerRequestEvidences,
        auditCustomerRequestEvidencesIsLoading,
        auditCustomerRequestEvidencesTotal,
        auditCustomerRequestAvailableEvidenceTypes,
        auditCustomerRequestEvidencesZipIsLoading,
        downloadAllEvidencesAsZip,
        loadEvidencesPage,
    } = sharedAuditHubEvidenceController;

    const {
        clientId,
        auditorFrameworkId,
        getRequestId: requestId,
    } = sharedCustomerRequestDetailsController;

    return (
        <AppDatatable
            isRowSelectionEnabled
            getRowId={(_row, index) => String(index)}
            isLoading={auditCustomerRequestEvidencesIsLoading}
            tableId="datatable-audit-hub-evidence-request-details"
            data={auditCustomerRequestEvidences}
            total={auditCustomerRequestEvidencesTotal}
            data-testid="EvidenceRequestDetailsView"
            data-id="U9j_XYCl"
            columns={getEvidenceRequestDetailsColumns()}
            bulkActionDropdownItems={sharedEvidenceBulkActionsModel.bulkActions}
            filterProps={getEvidenceRequestDetailsFilters(
                auditCustomerRequestAvailableEvidenceTypes,
            )}
            defaultPaginationOptions={
                EVIDENCE_REQUEST_DETAILS_PAGINATION_OPTIONS.defaultPaginationOptions
            }
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: t`Download all`,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: auditCustomerRequestEvidencesZipIsLoading,
                        a11yLoadingLabel: 'Downloading evidence',
                        onClick: () => {
                            downloadAllEvidencesAsZip();
                        },
                    },
                },
            ]}
            filterViewModeProps={{
                viewMode: 'unpinned',
            }}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Evidence`,
                description: t`No evidence were found`,
            }}
            onFetchData={loadEvidencesPage}
            onRowSelection={sharedEvidenceBulkActionsModel.handleRowSelection}
            onRowClick={({ row: evidenceData, _internal }) => {
                const { index: rowIndex } = _internal as { index: number };
                const controller = sharedAuditHubEvidenceViewerController as {
                    setSelectedEvidence: (
                        evidence: unknown,
                        rowIndex: number,
                    ) => void;
                };

                controller.setSelectedEvidence(evidenceData, rowIndex);

                navigate(
                    `/audit-hub/clients/${clientId}/audits/${auditorFrameworkId}/evidence-requests/${requestId}/evidence-viewer`,
                );
            }}
        />
    );
});
