import { isEmpty } from 'lodash-es';
import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import type {
    BulkAction,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, toJS, when } from '@globals/mobx';

export class EvidenceBulkActionsModel {
    selectedEvidence: AuditHubEvidenceResponseDto[] = [];
    isAllRowsSelected = false;

    constructor() {
        makeAutoObservable(this);
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'download-button',
                typeProps: {
                    startIconName: 'Download',
                    label: t`Download selection`,
                    level: 'tertiary',
                    onClick: () => {
                        this.handleDownloadSelected();
                    },
                },
            },
        ];
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        const allEvidence =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;

        if (isAllRowsSelected) {
            this.selectedEvidence = toJS(allEvidence);
        } else {
            const selectedIndices = Object.keys(selectedRows);

            // Map selected indices to actual evidence items
            this.selectedEvidence = selectedIndices
                .map((index) => toJS(allEvidence[parseInt(index)]))
                .filter(Boolean);
        }
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleDownloadSelected = (): void => {
        if (isEmpty(this.selectedEvidence)) {
            return;
        }

        this.downloadSequentially(this.selectedEvidence, 0);
    };

    downloadSequentially = (
        evidenceList: AuditHubEvidenceResponseDto[],
        currentIndex: number,
    ): void => {
        if (currentIndex >= evidenceList.length) {
            return;
        }

        const evidence = evidenceList[currentIndex];
        const {
            downloadEvidence,
            auditCustomerRequestEvidenceDownloadIsLoading,
        } = sharedAuditHubEvidenceController;

        if (
            evidence.signedUrl ||
            (evidence.fileData && evidence.fileName && evidence.fileType)
        ) {
            downloadEvidence(evidence);
            this.downloadSequentially(evidenceList, currentIndex + 1);
        } else {
            downloadEvidence(evidence);

            when(
                () => !auditCustomerRequestEvidenceDownloadIsLoading,
                () => {
                    this.downloadSequentially(evidenceList, currentIndex + 1);
                },
            );
        }
    };
}

export const sharedEvidenceBulkActionsModel = new EvidenceBulkActionsModel();
