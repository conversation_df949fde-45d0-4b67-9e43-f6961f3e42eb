import { openMapRisksModal } from '@components/controls';
import { handleOpenRiskPanel } from '@components/risks';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { ActionsCell } from '../cells/actions-cell';
import { ControlRiskTitleCell } from '../cells/control-risk-title-cell.component';
import { ControlRiskTreatmentCell } from '../cells/control-risk-treatment-cell.component';
import { RiskCodeCell } from '../cells/risk-code-cell.component';
import { RiskInherentScoreCell } from '../cells/risk-inherent-score-cell.component';
import { ControlRisksOwnerCell } from '../cells/risk-owner-cell.component';
import { RiskResidualScoreCell } from '../cells/risk-residual-score-cell.component';
import { CONTROL_RISKS_COLUMN_SIZES } from '../constants/control-risks-column-sizes.constants';

export class ControlRiskTableModel {
    controlId: number;

    constructor(controlId: number) {
        makeAutoObservable(this);
        this.controlId = controlId;
    }

    get emptyStateProps(): DatatableProps<RiskResponseDto>['emptyStateProps'] {
        return {
            illustrationName: 'RiskManagement',
            title: t`Track progress on risk treatment`,
            description: t`Map this control to impacted risks to automate your risk management program`,
        };
    }

    get tableActions(): DatatableProps<RiskResponseDto>['tableActions'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        return hasWriteControlPermission
            ? [
                  {
                      actionType: 'button',
                      id: 'map-risks-button',
                      typeProps: {
                          label: t`Map risks`,
                          onClick: action(() => {
                              openMapRisksModal({
                                  controlId: this.controlId,
                              });
                          }),
                      },
                  },
              ]
            : undefined;
    }

    get tableSearchProps(): DatatableProps<RiskResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    onRowClick: DatatableProps<RiskResponseDto>['onRowClick'] = ({ row }) => {
        handleOpenRiskPanel(row.id, row.riskId, 'CONTROLS_RISKS');
    };

    get columns(): DatatableProps<RiskResponseDto>['columns'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        return [
            ...(hasWriteControlPermission
                ? [
                      {
                          accessorKey: 'riskId',
                          id: 'actions',
                          header: '',
                          enableSorting: false,
                          cell: ActionsCell,
                          meta: {
                              shouldIgnoreRowClick: true,
                          },
                          minSize: CONTROL_RISKS_COLUMN_SIZES.SMALL,
                          maxSize: CONTROL_RISKS_COLUMN_SIZES.SMALL,
                      },
                  ]
                : []),
            {
                accessorKey: 'riskId',
                header: t`Code`,
                id: 'RISK_ID',
                enableSorting: true,
                cell: RiskCodeCell,
                minSize: CONTROL_RISKS_COLUMN_SIZES.SMALL,
                maxSize: CONTROL_RISKS_COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'title',
                header: t`Title`,
                id: 'RISK_TITLE',
                enableSorting: true,
                minSize: CONTROL_RISKS_COLUMN_SIZES.LARGE,
                maxSize: CONTROL_RISKS_COLUMN_SIZES.LARGE,
                cell: ControlRiskTitleCell,
            },
            {
                accessorKey: 'score',
                header: t`Inherent score`,
                id: 'RISK_SCORE',
                enableSorting: true,
                cell: RiskInherentScoreCell,
            },
            {
                accessorKey: 'residualScore',
                header: t`Residual Score`,
                id: 'RISK_RESIDUAL_SCORE',
                enableSorting: true,
                cell: RiskResidualScoreCell,
            },
            {
                accessorKey: 'treatmentPlan',
                header: t`Treatment`,
                id: 'RISK_TREATMENT',
                enableSorting: true,
                cell: ControlRiskTreatmentCell,
                minSize: CONTROL_RISKS_COLUMN_SIZES.MEDIUM,
                maxSize: CONTROL_RISKS_COLUMN_SIZES.MEDIUM,
            },
            {
                accessorKey: 'owners',
                header: t`Owners`,
                id: 'RISK_OWNERS',
                enableSorting: true,
                cell: ControlRisksOwnerCell,
            },
        ];
    }
}
