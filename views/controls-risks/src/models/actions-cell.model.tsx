import {
    sharedControlRisksController,
    sharedControlRisksMutationController,
} from '@controllers/controls';
import { Icon } from '@cosmos/components/icon';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, toJS, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export class ActionsCellModel {
    risk: RiskResponseDto;

    constructor(risk: RiskResponseDto) {
        makeAutoObservable(this);
        this.risk = risk;
    }

    get items(): SchemaDropdownItems {
        const { controlId } = sharedControlRisksController;
        const { isUnmappingPending } = sharedControlRisksMutationController;

        return [
            {
                id: 'unmap-risk-option',
                label: t`Unmap risk`,
                value: 'unmap',
                startSlot: <Icon name="Unlink" size="200" />,
                onClick: () => {
                    openConfirmationModal({
                        title: t`Unmap risk?`,
                        body: t`This risk will no longer be mapped to this control.`,
                        confirmText: t`Unmap`,
                        cancelText: t`Cancel`,
                        size: 'md',
                        type: 'danger',
                        onConfirm: action(() => {
                            sharedControlRisksMutationController.unMapRiskToControl(
                                controlId,
                                toJS(this.risk),
                            );
                            when(
                                () => !isUnmappingPending,
                                () => {
                                    closeConfirmationModal();
                                },
                            );
                        }),
                        onCancel: closeConfirmationModal,
                    });
                },
            },
        ];
    }
}
