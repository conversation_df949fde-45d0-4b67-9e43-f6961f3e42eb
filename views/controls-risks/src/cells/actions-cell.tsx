import type { Row } from '@cosmos/components/datatable';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ActionsCellModel } from '../models/actions-cell.model';

export const ActionsCell = observer(
    ({ row }: { row: Row<RiskResponseDto> }): React.JSX.Element => {
        const { items } = new ActionsCellModel(row.original);

        return (
            <SchemaDropdown
                isIconOnly
                label={t`Actions`}
                level="tertiary"
                startIconName="Action"
                data-testid="ActionsCell"
                data-id="s0nK5Fbh"
                colorScheme="neutral"
                items={items}
            />
        );
    },
);
