import { AppDatatable } from '@components/app-datatable';
import { sharedControlRisksController } from '@controllers/controls';
import { observer } from '@globals/mobx';
import { ControlRiskTableModel } from '../models/control-risk-table.model';

export const ControlsRisksView = observer((): React.JSX.Element => {
    const { controlId, controlRisks, total, isLoading, loadControlRisksPage } =
        sharedControlRisksController;

    const {
        emptyStateProps,
        tableActions,
        tableSearchProps,
        onRowClick,
        columns,
    } = new ControlRiskTableModel(controlId);

    return (
        <AppDatatable
            isFullPageTable
            isLoading={isLoading}
            tableId="control-risks-datatable"
            total={total}
            data={controlRisks}
            columns={columns}
            data-testid="ControlsRisksView"
            data-id="pFAJbs7c"
            emptyStateProps={emptyStateProps}
            tableActions={tableActions}
            tableSearchProps={tableSearchProps}
            onFetchData={loadControlRisksPage}
            onRowClick={onRowClick}
        />
    );
});
