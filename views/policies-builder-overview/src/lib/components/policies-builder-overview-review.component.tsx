import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';

export const PoliciesBuilderOverviewReviewComponent = (): React.JSX.Element => {
    return (
        <Card
            size="lg"
            title={t`Approval`}
            body={<div></div>}
            data-testid="PoliciesBuilderOverviewReviewComponent"
            data-id="j-C6Okgc"
            actions={[
                {
                    actionType: 'button',
                    id: 'button-action-skeleton',
                    typeProps: {
                        label: t`Edit approval settings`,
                        level: 'secondary',
                    },
                },
            ]}
        />
    );
};
