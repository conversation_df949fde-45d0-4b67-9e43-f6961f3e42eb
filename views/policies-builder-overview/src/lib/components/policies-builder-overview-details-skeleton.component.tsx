import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';

export const PoliciesBuilderOverviewDetailsSkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Card
                size="lg"
                title={t`Details`}
                data-testid="PoliciesBuilderOverviewDetailsSkeletonComponent"
                data-id="keol8JI4-skeleton"
                body={
                    <Stack direction="column" gap="4x">
                        {/* Policy name */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="25%"
                                barHeight="16px"
                            />
                            <Skeleton barCount={1} width="60%" />
                        </Stack>

                        {/* Renewal date */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="30%"
                                barHeight="16px"
                            />
                            <Skeleton barCount={1} width="40%" />
                        </Stack>

                        {/* Description */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="25%"
                                barHeight="16px"
                            />
                            <Skeleton barCount={2} width="90%" />
                        </Stack>

                        {/* Assigned to */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="25%"
                                barHeight="16px"
                            />
                            <Skeleton barCount={1} width="50%" />
                        </Stack>

                        {/* Frameworks section */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="25%"
                                barHeight="16px"
                            />
                            <Stack direction="row" gap="2x">
                                <Skeleton
                                    barCount={1}
                                    width="80px"
                                    barHeight="24px"
                                />
                                <Skeleton
                                    barCount={1}
                                    width="100px"
                                    barHeight="24px"
                                />
                                <Skeleton
                                    barCount={1}
                                    width="90px"
                                    barHeight="24px"
                                />
                                <Skeleton
                                    barCount={1}
                                    width="70px"
                                    barHeight="24px"
                                />
                            </Stack>
                        </Stack>

                        {/* Linked controls section */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="30%"
                                barHeight="16px"
                            />
                            <Stack direction="row" gap="2x">
                                <Skeleton
                                    barCount={1}
                                    width="70px"
                                    barHeight="24px"
                                />
                                <Skeleton
                                    barCount={1}
                                    width="80px"
                                    barHeight="24px"
                                />
                                <Skeleton
                                    barCount={1}
                                    width="75px"
                                    barHeight="24px"
                                />
                            </Stack>
                        </Stack>

                        {/* Policies replaced */}
                        <Stack direction="column" gap="1x">
                            <Skeleton
                                barCount={1}
                                width="35%"
                                barHeight="16px"
                            />
                            <Skeleton barCount={1} width="20%" />
                        </Stack>
                    </Stack>
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action-skeleton',
                        typeProps: {
                            label: t`Edit`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled: true,
                        },
                    },
                ]}
            />
        );
    };
