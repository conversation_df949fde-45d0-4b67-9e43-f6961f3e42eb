import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { PoliciesBuilderOverviewDetailsSkeletonComponent } from './policies-builder-overview-details-skeleton.component';
import { PoliciesBuilderOverviewOwnerSkeletonComponent } from './policies-builder-overview-owner-skeleton.component';
import { PoliciesBuilderOverviewReviewSkeletonComponent } from './policies-builder-overview-review-skeleton.component';

export const PoliciesBuilderOverviewSkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Grid
                gap="4x"
                columns="repeat(2, 1fr)"
                align="start"
                data-testid="PoliciesBuilderOverviewViewSkeleton"
                data-id="iLfzXsxd-skeleton"
            >
                <Box gridColumn="1">
                    <PoliciesBuilderOverviewDetailsSkeletonComponent />
                </Box>
                <Box gridColumn="2">
                    <Grid gap="4x" rows="max-content max-content">
                        <PoliciesBuilderOverviewReviewSkeletonComponent />
                        <PoliciesBuilderOverviewOwnerSkeletonComponent />
                    </Grid>
                </Box>
            </Grid>
        );
    };
