import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { PoliciesBuilderOverviewDetailsReadOnlyComponent } from './policies-builder-overview-details-readonly.component';

export const PoliciesBuilderOverviewDetailsComponent = observer(
    (): React.JSX.Element => {
        return (
            <Card
                size="lg"
                title={t`Details`}
                data-testid="PoliciesBuilderOverviewDetailsComponent"
                data-id="keol8JI4"
                body={<PoliciesBuilderOverviewDetailsReadOnlyComponent />}
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action',
                        typeProps: {
                            label: t`Edit`,
                            level: 'secondary',
                            onClick: () => {
                                alert('Edit button clicked');
                            },
                        },
                    },
                ]}
            />
        );
    },
);
