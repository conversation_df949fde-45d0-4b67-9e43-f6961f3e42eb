import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Text } from '@cosmos/components/text';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

export const PoliciesBuilderOverviewOwnerComponent = observer(
    (): React.JSX.Element => {
        const { policy } = sharedPolicyBuilderController;
        const { currentOwner: owner } = policy ?? {};

        return (
            <Card
                size="lg"
                title={t`Owner`}
                data-testid="PoliciesBuilderOverviewOwnerComponent"
                data-id="QhbItFVE"
                body={
                    <Grid gap="4x">
                        <Text data-id="text-owner-description">
                            The policy owner ensures the policy is functioning
                            correctly. Owners receive policy notifications and
                            are assigned tasks.
                        </Text>
                        <AvatarIdentity
                            imgSrc={owner?.avatarUrl ?? undefined}
                            primaryLabel={getFullName(
                                owner?.firstName,
                                owner?.lastName,
                            )}
                            fallbackText={getInitials(
                                `${owner?.firstName}  ${owner?.lastName}`,
                            )}
                        />
                    </Grid>
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action',
                        typeProps: {
                            label: t`Edit`,
                            level: 'secondary',
                            onClick: () => {
                                alert('Edit button clicked');
                            },
                        },
                    },
                ]}
            />
        );
    },
);
