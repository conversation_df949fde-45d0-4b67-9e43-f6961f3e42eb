import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';

export const PoliciesBuilderOverviewReviewSkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Card
                size="lg"
                title={t`Approval`}
                data-testid="PoliciesBuilderOverviewReviewSkeletonComponent"
                data-id="j-C6Okgc-skeleton"
                body={
                    <Grid gap="4x">
                        {/* Status badge skeleton */}
                        <Skeleton barCount={1} width="80px" barHeight="24px" />

                        {/* Avatar and name section */}
                        <Stack direction="row" gap="3x" align="center">
                            {/* Avatar skeleton - circular */}
                            <Skeleton
                                barCount={1}
                                width="40px"
                                barHeight="40px"
                            />
                            {/* Name and status section */}
                            <Grid gap="1x">
                                {/* Name skeleton */}
                                <Skeleton barCount={1} width="120px" />
                                {/* Status and date skeleton */}
                                <Stack direction="row" gap="2x" align="center">
                                    <Skeleton
                                        barCount={1}
                                        width="70px"
                                        barHeight="16px"
                                    />
                                    <Skeleton
                                        barCount={1}
                                        width="100px"
                                        barHeight="16px"
                                    />
                                </Stack>
                            </Grid>
                        </Stack>

                        {/* Footer note skeleton */}
                        <Skeleton barCount={1} width="140px" barHeight="14px" />
                    </Grid>
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action-skeleton',
                        typeProps: {
                            label: t`Edit approval settings`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled: true,
                        },
                    },
                ]}
            />
        );
    };
