import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';

export const PoliciesBuilderOverviewOwnerSkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Card
                size="lg"
                title={t`Owner`}
                data-testid="PoliciesBuilderOverviewOwnerSkeletonComponent"
                data-id="QhbItFVE-skeleton"
                body={
                    <Grid gap="4x">
                        {/* Description text */}
                        <Skeleton barCount={2} width="90%" />

                        {/* Avatar and name section */}
                        <Stack direction="row" gap="3x" align="center">
                            {/* Avatar skeleton - circular */}
                            <Skeleton
                                barCount={1}
                                width="40px"
                                barHeight="40px"
                            />
                            {/* Name skeleton */}
                            <Skeleton barCount={1} width="120px" />
                        </Stack>
                    </Grid>
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action-skeleton',
                        typeProps: {
                            label: t`Edit`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled: true,
                        },
                    },
                ]}
            />
        );
    };
