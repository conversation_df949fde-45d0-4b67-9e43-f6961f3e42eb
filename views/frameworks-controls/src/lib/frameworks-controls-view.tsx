import { useCallback, useMemo, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { ControlPanel } from '@components/controls';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import {
    sharedControlDetailsOrchestratorController,
    sharedControlsController,
} from '@controllers/controls';
import { panelController } from '@controllers/panel';
import {
    sharedRequirementAssociateControlsMutationController,
    sharedRequirementDetailsController,
} from '@controllers/requirements';
import type {
    DatatableRef,
    FetchDataResponseParams,
    TableAction,
} from '@cosmos/components/datatable';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedFrameworksModel } from '../../../../models/frameworks/src';
import { sharedFrameworksControlsDataTableModel } from '../models/frameworks-controls-data-table.model';

export const FrameworksControlsView = observer((): React.JSX.Element => {
    const datatableRef = useRef<DatatableRef>(null);

    const { controls, total, isLoading, loadPage } = sharedControlsController;
    const { requirement, isRequirementLoading } =
        sharedRequirementDetailsController;

    const { handleRowSelection } = sharedFrameworksModel;

    const { columns, tableId, emptyStateProps } =
        sharedFrameworksControlsDataTableModel;

    const handleConfirm = useCallback(
        (controlIds: number[]) => {
            const { handleAssociateControls } =
                sharedRequirementAssociateControlsMutationController;

            if (requirement) {
                handleAssociateControls(controlIds, requirement.id);
            }
        },
        [requirement],
    );

    const handleOpenModal = action(() => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (currentWorkspace) {
            openLinkControlsModalWithWorkspace({
                objectType: 'risk',
                onConfirm: (selectedControls) => {
                    const controlIds = selectedControls.map(
                        (item) => item.controlData.id,
                    );

                    handleConfirm(controlIds);
                },
                excludeControlIds: controls.map((control) => control.id),
                workspaceId: currentWorkspace.id,
            });
        }
    });

    const tableActions: TableAction[] = useMemo(() => {
        const { isMapControlsTestsEnabled } = sharedFeatureAccessModel;

        if (!isMapControlsTestsEnabled) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'map-controls-action-stack',
                level: 'secondary',
                typeProps: {
                    label: t`Map controls`,
                    onClick: handleOpenModal,
                },
            },
        ];
    }, [handleOpenModal]);

    const handleFetchData = useCallback(
        (param: FetchDataResponseParams) => {
            if (!requirement) {
                return;
            }

            loadPage(param, {
                requirementId: requirement.id,
                isArchived: false,
            });
        },
        [loadPage, requirement],
    );
    const handleRowClick = useCallback(
        ({ row }: { row: ControlListResponseDto }) => {
            action(() => {
                sharedControlDetailsOrchestratorController.load(row.id);
                panelController.openPanel({
                    id: 'evidence-control-panel',
                    content: () => (
                        <ControlPanel
                            controlSource="FRAMEWORK_REQUIREMENTS"
                            data-id="fsnIlFZ5"
                        />
                    ),
                });
            })();
        },
        [],
    );

    const bulkActionDropdownItems = useMemo(() => {
        return sharedFrameworksModel.bulkActions(datatableRef);
    }, []);

    return (
        <AppDatatable
            isRowSelectionEnabled
            isLoading={isLoading || isRequirementLoading}
            tableId={tableId}
            total={total}
            data={controls}
            columns={columns}
            getRowId={(row) => String(row.id)}
            data-testid="FrameworksControlsView"
            imperativeHandleRef={datatableRef}
            data-id="flg7uN2t"
            emptyStateProps={emptyStateProps}
            tableActions={tableActions}
            bulkActionDropdownItems={bulkActionDropdownItems}
            onFetchData={handleFetchData}
            onRowClick={handleRowClick}
            onRowSelection={handleRowSelection}
        />
    );
});
