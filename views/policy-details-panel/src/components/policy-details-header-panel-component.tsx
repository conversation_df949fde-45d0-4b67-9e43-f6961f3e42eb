import { panelController } from '@controllers/panel';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { ActionStack } from '@cosmos/components/action-stack';
import { PanelControls, PanelHeader } from '@cosmos/components/panel';
import { observer } from '@globals/mobx';
import { sharedPolicyDetailsModel } from '@models/policies';
import { useNavigate } from '@remix-run/react';

export const PolicyDetailsHeaderPanelComponent = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const {
            name,
            onNextPageClick,
            onPrevPageClick,
            currentIndex,
            totalItems,
        } = sharedPolicyDetailsModel;
        const { isPolicyLoading, policy: policyDetails } =
            sharedPolicyBuilderController;

        const navigateToDetails = (): void => {
            panelController.closePanel();
            navigate(
                `governance/policies/builder/${policyDetails?.id}/overview`,
            );
        };

        return (
            <>
                <PanelControls
                    closeButtonLabel="Close"
                    pagination={{
                        currentItem: currentIndex + 1,
                        onNextPageClick,
                        onPrevPageClick,
                        totalItems,
                    }}
                    onClose={() => {
                        panelController.closePanel();
                    }}
                />
                <PanelHeader
                    data-id="97LOuuOO"
                    data-testid="PolicyDetailsHeaderPanelComponent"
                    title={isPolicyLoading ? 'Loading...' : name}
                    slot={
                        <ActionStack
                            isFullWidth
                            data-id="control-panel-header-action-stack"
                            gap="3x"
                            actions={[
                                {
                                    actionType: 'button',
                                    id: 'control-panel-action',
                                    typeProps: {
                                        colorScheme: 'primary',
                                        endIconName: 'Expand',
                                        label: 'Open',
                                        level: 'secondary',
                                        onClick: () => {
                                            navigateToDetails();
                                        },
                                    },
                                },
                            ]}
                        />
                    }
                />
            </>
        );
    },
);
