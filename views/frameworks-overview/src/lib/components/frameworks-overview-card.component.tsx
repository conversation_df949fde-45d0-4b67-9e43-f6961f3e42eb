import { sharedRequirementDetailsController } from '@controllers/requirements';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Text } from '@cosmos/components/text';
import { ShowMore } from '@cosmos-lab/components/show-more';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const ADDITIONAL_INFO_BAR_COUNT = 2;

export const FrameworksOverviewViewCard = observer((): React.JSX.Element => {
    const { requirement, isRequirementLoading } =
        sharedRequirementDetailsController;

    return (
        <Grid
            gap={'xl'}
            data-testid="FrameworksOverviewViewCard"
            data-id="MnpB3GLy"
        >
            <KeyValuePair
                label={t`Code`}
                type={isRequirementLoading ? 'REACT_NODE' : 'TEXT'}
                value={isRequirementLoading ? <Skeleton /> : requirement?.name}
            />
            <KeyValuePair
                label={t`Control description`}
                type={isRequirementLoading ? 'REACT_NODE' : 'TEXT'}
                value={
                    isRequirementLoading ? (
                        <Skeleton />
                    ) : (
                        requirement?.description
                    )
                }
            />
            <KeyValuePair
                label={t`Description`}
                type={isRequirementLoading ? 'REACT_NODE' : 'TEXT'}
                value={
                    isRequirementLoading ? (
                        <Skeleton />
                    ) : (
                        requirement?.longDescription
                    )
                }
            />
            {requirement?.additionalInfo && (
                <KeyValuePair
                    label={t`Additional information`}
                    type={isRequirementLoading ? 'REACT_NODE' : 'TEXT'}
                    value={
                        isRequirementLoading ? (
                            <Skeleton />
                        ) : (
                            requirement.additionalInfo
                        )
                    }
                />
            )}
            {requirement?.additionalInfo2 && (
                <ShowMore
                    content={
                        isRequirementLoading ? (
                            <Skeleton barCount={ADDITIONAL_INFO_BAR_COUNT} />
                        ) : (
                            <>
                                <Text size="200" as="p">
                                    {requirement.additionalInfo2}
                                </Text>
                                {requirement.additionalInfo3 && (
                                    <Text size="200" as="p">
                                        {requirement.additionalInfo3}
                                    </Text>
                                )}
                            </>
                        )
                    }
                />
            )}
        </Grid>
    );
});
