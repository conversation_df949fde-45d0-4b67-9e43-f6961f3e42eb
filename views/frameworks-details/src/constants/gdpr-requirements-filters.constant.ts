import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getGDPRRequirementsFilters(): FilterProps {
    const filterLabel = t`Chapters`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Principles`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.GDPR_PRINCIPLES
                    ],
                },
                {
                    label: t`Data Subject Rights`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.GDPR_RIGHTS_OF_THE_DATA_SUBJECT
                    ],
                },
                {
                    label: t`Controller & Processor`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.GDPR_CONTROLLER_AND_PROCESSOR
                    ],
                },
                {
                    label: t`International Data Transfers`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .GDPR_TRANSFERS_OF_PERSONNEL_DATA_TO_THIRD_COUNTRIES_OR_INTERNATIONAL_ORGANIZATIONS
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
