import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import { createRequirementFilters } from './base-requirements-filters.constant';

export function getISO27701RequirementsFilters(): FilterProps {
    return createRequirementFilters([
        {
            filterType: 'select',
            id: 'category',
            label: t`Category`,
            options: [
                {
                    groupHeader: t`PIMS`,
                    items: [
                        {
                            id: 'iso27701-pims-specific-requirements',
                            label: t`PIMS-Specific Requirements, ISO 27001`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_PIMS_SPECIFIC_REQUIREMENTS
                            ],
                        },
                        {
                            id: 'iso27701-pims-specific-guidance',
                            label: t`PIMS-Specific Guidance, ISO 27002`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_PIMS_SPECIFIC_GUIDANCE
                            ],
                        },
                    ],
                },
                {
                    groupHeader: t`Guidance for PII Controllers (Annex A)`,
                    items: [
                        {
                            id: 'iso27701-annex-a-conditions-for-collection-and-processing',
                            label: t`Conditions for Collection and Processing`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_A_CONDITIONS_FOR_COLLECTION_AND_PROCESSING
                            ],
                        },
                        {
                            id: 'iso27701-annex-a-obligations-to-pii-principles',
                            label: t`Obligations to PII Principles`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_A_OBLIGATIONS_TO_PII_PRINCIPLES
                            ],
                        },
                        {
                            id: 'iso27701-annex-a-privacy-by-design-and-privacy-by-default',
                            label: t`Privacy by Design and Privacy by Default`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_A_PRIVACY_BY_DESIGN_AND_PRIVACY_BY_DEFAULT
                            ],
                        },
                        {
                            id: 'iso27701-annex-a-pii-sharing-transfer-and-disclosure',
                            label: t`PII Sharing Transfer and Disclosure`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_A_PII_SHARING_TRANSFER_AND_DISCLOSURE
                            ],
                        },
                    ],
                },
                {
                    groupHeader: t`Guidance for PII Processors (Annex B)`,
                    items: [
                        {
                            id: 'iso27701-annex-b-conditions-for-collection-and-processing',
                            label: t`Conditions for Collection and Processing`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_B_CONDITIONS_FOR_COLLECTION_AND_PROCESSING
                            ],
                        },
                        {
                            id: 'iso27701-annex-b-obligations-to-pii-principles',
                            label: t`Obligations to PII Principles`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_B_OBLIGATIONS_TO_PII_PRINCIPLES
                            ],
                        },
                        {
                            id: 'iso27701-annex-b-privacy-by-design-and-privacy-by-default',
                            label: t`Privacy by Design and Privacy by Default`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_B_PRIVACY_BY_DESIGN_AND_PRIVACY_BY_DEFAULT
                            ],
                        },
                        {
                            id: 'iso27701-annex-b-pii-sharing-transfer-and-disclosure',
                            label: t`PII Sharing Transfer and Disclosure`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO277012019_ANNEX_B_PII_SHARING_TRANSFER_AND_DISCLOSURE
                            ],
                        },
                    ],
                },
            ],
        },
    ]);
}
