import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import { RequirementIndexCategory, TrustServiceCriteria } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets subcategory options for a specific SOC 2 topic.
 */
function getSOC2SubcategoryOptions(topic: TrustServiceCriteria) {
    switch (topic) {
        case TrustServiceCriteria.SECURITY: {
            return [
                {
                    label: t`Control Environment`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_CONTROL_ENVIRONMENT
                    ],
                },
                {
                    label: t`Communication and Information`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_COMMUNICATION_AND_INFORMATION
                    ],
                },
                {
                    label: t`Risk Assessment`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_RISK_ASSESSMENT
                    ],
                },
                {
                    label: t`Monitoring Activities`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_MONITORING_ACTIVITIES
                    ],
                },
                {
                    label: t`Control Activities`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_CONTROL_ACTIVITIES
                    ],
                },
                {
                    label: t`Logical and Physical Access Controls`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_LOGICAL_AND_PHYSICAL_ACCESS_CONTROLS
                    ],
                },
                {
                    label: t`System Operations`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_SYSTEM_OPERATIONS
                    ],
                },
                {
                    label: t`Change Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_CHANGE_MANAGEMENT
                    ],
                },
                {
                    label: t`Risk Mitigation`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_RISK_MITIGATION
                    ],
                },
            ];
        }
        case TrustServiceCriteria.AVAILABILITY: {
            return [
                {
                    label: t`Availability`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_AVAILABILITY
                    ],
                },
            ];
        }
        case TrustServiceCriteria.PROCESS_INTEGRITY: {
            return [
                {
                    label: t`Processing Integrity`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_PROCESS_INTEGRITY
                    ],
                },
            ];
        }
        case TrustServiceCriteria.CONFIDENTIALITY: {
            return [
                {
                    label: t`Confidentiality`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOC_2_CONFIDENTIALITY
                    ],
                },
            ];
        }
        case TrustServiceCriteria.PRIVACY: {
            return [
                {
                    label: t`Notice and Communication of Requirements Related to Privacy`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_NOTICE_AND_COMMUNICATION_OF_REQUIREMENTS_RELATED_TO_PRIVACY
                    ],
                },
                {
                    label: t`Choice and Consent`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_CHOICE_AND_CONSENT
                    ],
                },
                {
                    label: t`Collection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_COLLECTION
                    ],
                },
                {
                    label: t`Use, Retention and Disposal`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_USE_RETENTION_AND_DISPOSAL
                    ],
                },
                {
                    label: t`Access`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_ACCESS
                    ],
                },
                {
                    label: t`Disclosure and Notification`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_DISCLOSURE_AND_NOTIFICATION
                    ],
                },
                {
                    label: t`Quality`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_QUALITY
                    ],
                },
                {
                    label: t`Monitoring and Enforcement`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .SOC_2_PRIVACY_CRITERIA_RELATED_TO_MONITORING_AND_ENFORCEMENT
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Gets SOC 2 topic options with labels and values.
 */
function getSOC2TopicOptions() {
    return [
        {
            label: t`Security`,
            value: TrustServiceCriteria[TrustServiceCriteria.SECURITY],
        },
        {
            label: t`Availability`,
            value: TrustServiceCriteria[TrustServiceCriteria.AVAILABILITY],
        },
        {
            label: t`Process Integrity`,
            value: TrustServiceCriteria[TrustServiceCriteria.PROCESS_INTEGRITY],
        },
        {
            label: t`Confidentiality`,
            value: TrustServiceCriteria[TrustServiceCriteria.CONFIDENTIALITY],
        },
        {
            label: t`Privacy`,
            value: TrustServiceCriteria[TrustServiceCriteria.PRIVACY],
        },
    ];
}

/**
 * Creates a subcategory filter based on the selected topic.
 *
 * @param selectedTopic - The selected topic value to determine subcategory options.
 * @returns Subcategory filter object or null if no topic is selected.
 */
function createSubcategoryFilter(selectedTopic?: string): Filter | null {
    if (!selectedTopic) {
        return null;
    }

    const topicEnum = TrustServiceCriteria[
        selectedTopic as keyof typeof TrustServiceCriteria
    ] as TrustServiceCriteria;

    const options = getSOC2SubcategoryOptions(topicEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the topic label from the options
    const topicOption = getSOC2TopicOptions().find(
        (option) => option.value === selectedTopic,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedTopic}`,
        label: topicOption?.label || t`Subcategory`,
        options,
    };
}

/**
 * Gets SOC 2 requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedTopic - Optional selected topic to show relevant subcategories.
 * @returns FilterProps configuration for SOC 2 requirements.
 */
export function getSOC2RequirementsFilters(
    selectedTopic?: string,
): FilterProps {
    const filterLabel = t`Trust service categories`;

    const filters: Filter[] = [
        createCategoryFilter(getSOC2TopicOptions(), filterLabel),
    ];

    // Add subcategory filter if a topic is selected
    const subcategoryFilter = createSubcategoryFilter(selectedTopic);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
