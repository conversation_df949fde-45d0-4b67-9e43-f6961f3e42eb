import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getPCI4RequirementsFilters(): FilterProps {
    const filterLabel = t`Themes`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Network Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_NETWORK_SECURITY
                    ],
                },
                {
                    label: t`Secure Configurations`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_SECURE_CONFIGURATIONS
                    ],
                },
                {
                    label: t`Data Storage Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_DATA_STORAGE_PROTECTION
                    ],
                },
                {
                    label: t`Data Transmission Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_DATA_TRANSMISSION_PROTECTION
                    ],
                },
                {
                    label: t`Malware Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_MALWARE_PROTECTION
                    ],
                },
                {
                    label: t`Secure Development and Maintenance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_SECURE_DEVELOPMENT_AND_MAINTENANCE
                    ],
                },
                {
                    label: t`Access Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_ACCESS_MANAGEMENT
                    ],
                },
                {
                    label: t`Identification and Authentication`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`Physical Access Control`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_PHYSICAL_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Logging and Monitoring`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_LOGGING_AND_MONITORING
                    ],
                },
                {
                    label: t`Security Testing`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI4_SECURITY_TESTING
                    ],
                },
                {
                    label: t`Organizational Policies and Programs`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_ORGANIZATIONAL_POLICIES_AND_PROGRAMS
                    ],
                },
                {
                    label: t`Appendix 1 - Multi-Tenant Service Providers`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_APPENDIX_1_MULTI_TENANT_SERVICE_PROVIDERS
                    ],
                },
                {
                    label: t`Appendix 2 - Entities Using SSL/Early TLS`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_APPENDIX_2_ENTITIES_USING_SSL_EARLY_TLS
                    ],
                },
                {
                    label: t`App 3: Designated Entities Supplemental Validation`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .PCI4_APPENDIX_3_DESIGNATED_ENTITIES_SUPPLEMENTAL_VALIDATION
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
