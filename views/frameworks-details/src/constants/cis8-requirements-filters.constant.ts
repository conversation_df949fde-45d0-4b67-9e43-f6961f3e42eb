import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getCIS8RequirementsFilters(): FilterProps {
    const filterLabel = t`Control`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Access Control Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_ACCESS_CONTROL_MANAGEMENT
                    ],
                },
                {
                    label: t`Account Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_ACCOUNT_MANAGEMENT
                    ],
                },
                {
                    label: t`Application Software Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_APPLICATION_SOFTWARE_SECURITY
                    ],
                },
                {
                    label: t`Audit Log Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_AUDIT_LOG_MANAGEMENT
                    ],
                },
                {
                    label: t`Continuous Vulnerability Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_CONTINUOUS_VULNERABILITY_MANAGEMENT
                    ],
                },
                {
                    label: t`Data Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_DATA_PROTECTION
                    ],
                },
                {
                    label: t`Data Recovery`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_DATA_RECOVERY
                    ],
                },
                {
                    label: t`Email & Web Browser Protections`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_EMAIL_AND_WEB_BROWSER_PROTECTIONS
                    ],
                },
                {
                    label: t`Incident Response Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_INCIDENT_RESPONSE_MANAGEMENT
                    ],
                },
                {
                    label: t`Inventory & Control of Enterprise Assets`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_INVENTORY_AND_CONTROL_OF_ENTERPRISE_ASSETS
                    ],
                },
                {
                    label: t`Inventory & Control of Software Assets`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_INVENTORY_AND_CONTROL_OF_SOFTWARE_ASSETS
                    ],
                },
                {
                    label: t`Malware Defenses`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_MALWARE_DEFENSES
                    ],
                },
                {
                    label: t`Network Infrastructure Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_NETWORK_INFRASTRUCTURE_MANAGEMENT
                    ],
                },
                {
                    label: t`Network Monitoring & Defense`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_NETWORK_MONITORING_AND_DEFENSE
                    ],
                },
                {
                    label: t`Penetration Testing`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_PENETRATION_TESTING
                    ],
                },
                {
                    label: t`Security Awareness & Skills Training`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_SECURITY_AWARENESS_AND_SKILLS_TRAINING
                    ],
                },
                {
                    label: t`Secure Configuration of Enterprise Assets & Software`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_SECURE_CONFIGURATION_OF_ENTERPRISE_ASSETS_AND_SOFTWARE
                    ],
                },
                {
                    label: t`Service Provider Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_SERVICE_PROVIDER_MANAGEMENT
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
