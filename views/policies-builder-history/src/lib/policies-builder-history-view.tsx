import { isNil } from 'lodash-es';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { activeGovernancePoliciesHistoryController } from '@controllers/governance-policies-history';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Card } from '@cosmos/components/card';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { observer } from '@globals/mobx';
import { POLICIES_BUILDER_HISTORY_COLUMNS } from './policies-builder-history-view.constants';
import { transformPolicyHistoryData } from './util/policies-builder-transform';

export const PoliciesBuilderHistoryView = observer((): React.JSX.Element => {
    const {
        loadPolicyVersionHistory,
        isLoading,
        policyVersionsTotal,
        policyVersions,
    } = activeGovernancePoliciesHistoryController;
    const { policy } = sharedPolicyBuilderController;
    const transformedData = policyVersions.map(transformPolicyHistoryData);

    if (isLoading) {
        return (
            <Card
                size="lg"
                title="Details"
                data-testid="PoliciesBuilderHistoryDetailsComponent"
                data-id="keol8JI4"
                body={<Loader isSpinnerOnly label={'Loading...'} />}
            />
        );
    }

    if (isNil(policy?.latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <Grid
            gap="lg"
            data-testid="PoliciesBuilderHistoryView"
            data-id="ft2_8cCi"
        >
            <Datatable
                isLoading={isLoading}
                tableId="datatable-policies-history"
                data-id="datatable-policies-history"
                data={transformedData}
                columns={POLICIES_BUILDER_HISTORY_COLUMNS}
                total={policyVersionsTotal}
                emptyStateProps={{
                    title: 'title',
                    description: 'description',
                }}
                tableSearchProps={{
                    hideSearch: true,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: 'Pin filters to page',
                        toggleUnpinnedLabel: 'Move filters to dropdown',
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadPolicyVersionHistory}
            />
        </Grid>
    );
});
