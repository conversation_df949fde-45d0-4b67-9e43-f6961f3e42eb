import { snackbarController } from '@controllers/snackbar';
import { auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

class AuditHubAuditorValidatePersonnelController {
    auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation =
        new ObservedMutation(
            auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation,
        );

    constructor() {
        makeAutoObservable(this);
    }

    validatePersonnelStartDateEndDate = (auditId: string) => {
        this.auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation.mutate(
            {
                path: { auditId },
            },
        );

        when(
            () =>
                !this
                    .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
                    .isPending,
            () => {
                if (
                    this
                        .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
                        .hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'validate-personnel-start-end-date-error',
                        props: {
                            title: t`Failed to validate personnel dates`,
                            description: t`Failed to validate personnel start and end dates.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    get isValidating(): boolean {
        return this
            .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
            .isPending;
    }

    get validationData() {
        return this
            .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
            .response;
    }

    get hasValidationError(): boolean {
        return this
            .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
            .hasError;
    }

    get validationError() {
        return this
            .auditorFrameworkControllerValidatePersonnelStartDateEndDateMutation
            .error;
    }
}

export const sharedAuditHubAuditorValidatePersonnelController =
    new AuditHubAuditorValidatePersonnelController();
