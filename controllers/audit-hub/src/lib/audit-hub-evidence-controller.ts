import { isEmpty, isObject, isString } from 'lodash-es';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    auditHubControllerGetAuditCustomerRequestEvidencesOptions,
    auditHubControllerGetAuditEvidenceDownloadOptions,
    auditHubControllerGetAuditEvidencesZipOptions,
} from '@globals/api-sdk/queries';
import type {
    AuditHubEvidenceResponseDto,
    AuditHubEvidenceTypeEnum,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import {
    downloadBase64AsFile,
    downloadFileFromSignedUrl,
} from '@helpers/download-file';
import { ERROR_IDS } from './constants/errors.constants';
import { getEvidenceTypeApiValue } from './constants/evidence-type-api-value-map.js';

class AuditHubEvidenceController {
    auditCustomerRequestEvidencesQuery = new ObservedQuery(
        auditHubControllerGetAuditCustomerRequestEvidencesOptions,
    );

    auditCustomerRequestEvidenceDownloadQuery = new ObservedQuery(
        auditHubControllerGetAuditEvidenceDownloadOptions,
    );

    auditCustomerRequestEvidencesZipQuery = new ObservedQuery(
        auditHubControllerGetAuditEvidencesZipOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get auditCustomerRequestEvidences(): AuditHubEvidenceResponseDto[] {
        return this.auditCustomerRequestEvidencesQuery.data?.data ?? [];
    }

    get auditCustomerRequestEvidencesIsLoading(): boolean {
        return this.auditCustomerRequestEvidencesQuery.isLoading;
    }

    get auditCustomerRequestEvidencesTotal(): number {
        return this.auditCustomerRequestEvidencesQuery.data?.total ?? 0;
    }

    get auditCustomerRequestAvailableEvidenceTypes(): AuditHubEvidenceTypeEnum[] {
        return (
            this.auditCustomerRequestEvidencesQuery.data
                ?.availableEvidenceTypes ?? []
        );
    }

    get auditCustomerRequestEvidenceDownloadIsLoading(): boolean {
        return this.auditCustomerRequestEvidenceDownloadQuery.isLoading;
    }

    get auditCustomerRequestEvidencesZipIsLoading(): boolean {
        return this.auditCustomerRequestEvidencesZipQuery.isLoading;
    }

    get auditCustomerRequestEvidencesZipData(): {
        signedUrl?: string;
        fileName?: string;
    } | null {
        return this.auditCustomerRequestEvidencesZipQuery.data as {
            signedUrl?: string;
            fileName?: string;
        } | null;
    }

    loadEvidencesPage = (params: FetchDataResponseParams): void => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            return;
        }

        this.#loadAuditCustomerRequestEvidences(
            String(auditId),
            Number(requestId),
            params,
        );
    };

    #loadAuditCustomerRequestEvidences = (
        auditId: string,
        customerRequestId: number,
        params: FetchDataResponseParams,
    ): void => {
        const { pagination, globalFilter, sorting } = params;
        const { pageSize, page = 1 } = pagination;
        const { filters: { types } = {}, search } = globalFilter;

        type Query = Required<
            Parameters<
                typeof auditHubControllerGetAuditCustomerRequestEvidencesOptions
            >
        >[0]['query'];

        const extractValue = (item: unknown): AuditHubEvidenceTypeEnum => {
            if (isObject(item) && 'value' in item) {
                return item.value as AuditHubEvidenceTypeEnum;
            }

            return item as AuditHubEvidenceTypeEnum;
        };

        let typesArray: AuditHubEvidenceTypeEnum[] | undefined;

        if (types.value) {
            typesArray = Array.isArray(types.value)
                ? types.value.map(extractValue)
                : [extractValue(types.value)];
        }

        const query: Query = {
            page,
            limit: pageSize,
            q: search ?? undefined,
            types: typesArray,
        };

        if (isEmpty(sorting)) {
            query.sort = 'NAME';
            query.sortDir = 'ASC';
        } else {
            query.sort = sorting[0].id as 'NAME' | 'TYPE' | 'DATE';
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.auditCustomerRequestEvidencesQuery.load({
            query,
            path: {
                auditId,
                customerRequestId,
            },
        });
    };

    /**
     * Generates and downloads evidence automatically when signedUrl and fileData are not available.
     */
    generateAndDownloadEvidence = (
        evidence: AuditHubEvidenceResponseDto,
    ): void => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            snackbarController.addSnackbar({
                id: ERROR_IDS.EVIDENCE_GENERATION_MISSING_PARAMS,
                props: {
                    title: t`Unable to generate evidence`,
                    description: t`Missing required audit or request information. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const downloadQuery = new ObservedQuery(
            auditHubControllerGetAuditEvidenceDownloadOptions,
        );

        downloadQuery.load({
            path: {
                auditId: String(auditId),
                customerRequestId: Number(requestId),
            },
            query: {
                type: getEvidenceTypeApiValue(
                    evidence.type,
                ) as AuditHubEvidenceTypeEnum,
                artifact: evidence.artifact,
                controlCode: evidence.controlCodes[0],
            },
        });

        when(
            () => !downloadQuery.isLoading,
            () => {
                if (downloadQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: ERROR_IDS.EVIDENCE_GENERATION,
                        props: {
                            title: t`Failed to generate evidence`,
                            description: t`An error occurred while generating the evidence. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const downloadEvidence = downloadQuery.data;

                if (!downloadEvidence) {
                    snackbarController.addSnackbar({
                        id: ERROR_IDS.EVIDENCE_GENERATION_NO_URL,
                        props: {
                            title: t`Download failed`,
                            description: t`No download data was generated. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const { fileData, fileName, fileType } = downloadEvidence;

                if (
                    isString(fileData) &&
                    isString(fileName) &&
                    isString(fileType)
                ) {
                    downloadBase64AsFile(fileData, fileName, fileType);
                } else {
                    snackbarController.addSnackbar({
                        id: ERROR_IDS.EVIDENCE_GENERATION_NO_URL,
                        props: {
                            title: t`Download failed`,
                            description: t`No download link was generated. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    /**
     * Smart method to download evidence that automatically handles
     * different cases: signedUrl, fileData, or automatic generation.
     */
    downloadEvidence = (evidence: AuditHubEvidenceResponseDto): void => {
        const { signedUrl, fileData, fileName, fileType } = evidence;

        // Case 1: If there's a signedUrl, use it directly
        if (signedUrl) {
            downloadFileFromSignedUrl(signedUrl);

            return;
        }

        // Case 2: If there's fileData, convert to blob and download
        if (isString(fileData) && isString(fileName) && isString(fileType)) {
            downloadBase64AsFile(fileData, fileName, fileType);

            return;
        }

        // Case 3: If neither exists, generate evidence automatically
        this.generateAndDownloadEvidence(evidence);
    };

    /**
     * Downloads all evidences as a ZIP file.
     */
    downloadAllEvidencesAsZip = (): void => {
        if (this.auditCustomerRequestEvidencesZipQuery.isLoading) {
            return;
        }

        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            snackbarController.addSnackbar({
                id: ERROR_IDS.EVIDENCE_GENERATION_MISSING_PARAMS,
                props: {
                    title: t`Unable to download ZIP`,
                    description: t`Missing required audit or request information. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        snackbarController.addSnackbar({
            id: 'zip-download-started',
            props: {
                title: t`Preparing ZIP file`,
                description: t`Generating ZIP file with all evidence files. This may take a moment...`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });

        this.auditCustomerRequestEvidencesZipQuery.load({
            path: {
                auditId: String(auditId),
                customerRequestId: Number(requestId),
            },
        });

        when(
            () => !this.auditCustomerRequestEvidencesZipQuery.isLoading,
            () => {
                if (this.auditCustomerRequestEvidencesZipQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: 'zip-download-error',
                        props: {
                            title: t`ZIP download failed`,
                            description: t`An error occurred while generating the ZIP file. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const zipData = this.auditCustomerRequestEvidencesZipData;

                if (!zipData?.signedUrl) {
                    snackbarController.addSnackbar({
                        id: 'zip-download-no-url',
                        props: {
                            title: t`ZIP download failed`,
                            description: t`No download link was generated. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                downloadFileFromSignedUrl(zipData.signedUrl);

                snackbarController.addSnackbar({
                    id: 'zip-download-success',
                    props: {
                        title: t`ZIP download started`,
                        description: t`Your evidence ZIP file download has started.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedAuditHubEvidenceController =
    new AuditHubEvidenceController();
