import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import {
    auditorControllerGenerateControlEvidencePackageMutation,
    companiesControllerGetAllOptions,
    customerRequestControllerCreateCustomerRequestsFromRequirementsMutation,
    customerRequestControllerCreateCustomerRequestsMutation,
    customerRequestControllerGetRequestsFileTemplateOptions,
    customerRequestControllerValidateCsvFileMutation,
} from '@globals/api-sdk/queries';
import type {
    CreateCustomerRequestRequestDto,
    CustomerRequestFileValidationResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    toJS,
} from '@globals/mobx';
import { sharedAuditHubController } from './audit-hub-controller';
import { ERROR_IDS, ERROR_MESSAGES } from './constants/errors.constants';
import { formatControlEvidenceRequest } from './helpers/control-evidence.helper';
import type { SampleWizardData } from './types/wizard.data.types';

class AuditHubAuditController {
    wizardData: Partial<SampleWizardData> = {};
    auditorFrameworkId: string | null = null;
    clientId: string | null = null;

    getRequestFileTemplate = new ObservedQuery(
        customerRequestControllerGetRequestsFileTemplateOptions,
    );

    createCustomerRequests = new ObservedMutation(
        customerRequestControllerCreateCustomerRequestsMutation,
    );

    generateControlEvidencePackage = new ObservedMutation(
        auditorControllerGenerateControlEvidencePackageMutation,
    );

    validateCsv = new ObservedMutation(
        customerRequestControllerValidateCsvFileMutation,
    );

    getAllCompanies = new ObservedQuery(companiesControllerGetAllOptions);

    createCustomerRequestsFromRequirements = new ObservedMutation(
        customerRequestControllerCreateCustomerRequestsFromRequirementsMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    addSampleWizardData = (data: Partial<SampleWizardData>) => {
        this.wizardData = {
            ...this.wizardData,
            ...data,
        };
    };

    loadRequestTemplate = (): void => {
        this.getRequestFileTemplate.load();
    };

    startAudit = async (): Promise<void> => {
        try {
            const { auditByIdData } = sharedAuditHubController;
            const shouldGenerateRequirementsFromFile =
                this.wizardData.evidenceOption === 'use-custom';

            if (shouldGenerateRequirementsFromFile) {
                await this._generateControlEvidenceFromFile();
            } else {
                await this._generateControlEvidencesFromRequirements();
            }

            this.loadAllCompanies(Number(auditByIdData?.framework.productId));
        } catch (error) {
            this._handleError(
                ERROR_IDS.AUDIT_START,
                ERROR_MESSAGES.AUDIT_START_FAILED,
                ERROR_MESSAGES.AUDIT_START_DESCRIPTION,
            );
            throw error;
        }
    };

    get requestTemplate(): string | null {
        return this.getRequestFileTemplate.data;
    }

    get sampleWizardData(): Partial<SampleWizardData> {
        return toJS(this.wizardData);
    }

    get csvValidationResponse() {
        return this.validateCsv.response;
    }

    _handleError = (id: string, title: string, description: string): void => {
        snackbarController.addSnackbar({
            id,
            props: {
                title,
                description,
                severity: 'critical',
                closeButtonAriaLabel: 'Close',
            },
        });
    };

    _validateCsvFile = async (
        file: File,
    ): Promise<CustomerRequestFileValidationResponseDto | undefined> => {
        if (!this.auditorFrameworkId) {
            throw new Error(
                'Auditor framework ID is required for CSV validation',
            );
        }

        await this.validateCsv.mutateAsync({
            body: {
                file,
                auditorFrameworkId: this.auditorFrameworkId,
            },
        });

        const validationResponse = this.validateCsv.response;

        if (validationResponse?.status === 'VALID') {
            return validationResponse;
        }

        this._handleError(
            ERROR_IDS.CSV_VALIDATION,
            ERROR_MESSAGES.CSV_VALIDATION_FAILED,
            ERROR_MESSAGES.CSV_VALIDATION_DESCRIPTION,
        );

        return undefined;
    };

    _generateControlEvidenceFromFile = async (): Promise<void> => {
        try {
            const file =
                this.wizardData.customEvidenceRequest?.evidenceRequestList?.[0];

            if (!file) {
                throw new Error(ERROR_MESSAGES.NO_FILE_PROVIDED);
            }

            const validationResponse = await this._validateCsvFile(file);

            this.addSampleWizardData({
                customEvidenceRequest: {
                    requests: validationResponse?.requests,
                },
            });

            await this.createCustomerRequests.mutateAsync({
                body: {
                    isBulkUpload: false,
                    isWizardUpload: true,
                    requests: (this.wizardData.customEvidenceRequest
                        ?.requests ?? []) as CreateCustomerRequestRequestDto[],
                },
            });

            await this._generateControlEvidence();
        } catch (error) {
            this._handleError(
                ERROR_IDS.CONTROL_EVIDENCE,
                ERROR_MESSAGES.CONTROL_EVIDENCE_FAILED,
                ERROR_MESSAGES.CONTROL_EVIDENCE_DESCRIPTION,
            );
            throw error;
        }
    };

    _generateControlEvidencesFromRequirements = async (): Promise<void> => {
        if (!this.auditorFrameworkId) {
            throw new Error('Auditor framework ID is required');
        }

        await this._generateControlEvidence();
        await this.createCustomerRequestsFromRequirements.mutateAsync({
            body: {
                auditorFrameworkId: this.auditorFrameworkId,
            },
        });
    };

    _generateControlEvidence = async (): Promise<void> => {
        const { auditByIdData } = sharedAuditHubController;
        const { entryId } = sharedCurrentUserController;

        if (!auditByIdData) {
            throw new Error(ERROR_MESSAGES.AUDIT_DATA_UNAVAILABLE);
        }

        const request = formatControlEvidenceRequest(
            this.sampleWizardData,
            auditByIdData,
        );

        await this.generateControlEvidencePackage.mutateAsync({
            path: {
                entryId,
                xProductId: auditByIdData.framework.productId,
            },
            body: {
                ...request,
            },
        });

        // Invalidate related queries after successful control evidence generation
        this._invalidateAuditDetailsQueries();
    };

    /**
     * Invalidates queries that need to be refreshed after sample evidence changes.
     * This ensures the AuditorClientAuditDetailsView displays updated data.
     */
    _invalidateAuditDetailsQueries = (): void => {
        const { auditByIdData } = sharedAuditHubController;

        if (auditByIdData?.framework) {
            logger.info({
                message:
                    'Invalidating audit details queries after sample evidence changes',
                additionalInfo: {
                    auditId: auditByIdData.framework.id,
                    frameworkType: auditByIdData.framework.type,
                },
            });

            // Invalidate audit summary data (used in donut chart)
            sharedAuditorController.auditSummaryByIdQuery.invalidate();

            // Invalidate customer requests list (used in request table)
            sharedCustomerRequestsController.customerRequestListQuery.invalidate();

            // Invalidate auditor client audit data (used in assigned auditors table)
            sharedAuditHubAuditorClientAuditController.auditorsQuery.invalidate();
            sharedAuditHubAuditorClientAuditController.auditDatesQuery.invalidate();
        }
    };

    loadAllCompanies = (productId: number): void => {
        if (!this.auditorFrameworkId) {
            throw new Error('Auditor framework ID is required');
        }

        this.getAllCompanies.load({
            query: {
                auditorFrameworkId: this.auditorFrameworkId,
            },
            path: {
                xProductId: productId,
            },
        });
    };
}

export const sharedAuditHubAuditController = new AuditHubAuditController();
