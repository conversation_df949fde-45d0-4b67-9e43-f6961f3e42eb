export const ERROR_MESSAGES = {
    AUDIT_START_FAILED: 'Failed to start audit',
    AUDIT_START_DESCRIPTION:
        'An error occurred while starting the audit. Try again later.',
    CSV_VALIDATION_FAILED: 'CSV validation failed',
    CSV_VALIDATION_DESCRIPTION: 'Please check the file and try again',
    CONTROL_EVIDENCE_FAILED: 'Failed to generate control evidence',
    CONTROL_EVIDENCE_DESCRIPTION:
        'An error occurred while generating the control evidence. Try again later.',
    NO_FILE_PROVIDED: 'No file provided for validation',
    AUDIT_DATA_UNAVAILABLE: 'Audit data is not available',
} as const;

export const ERROR_IDS = {
    AUDIT_START: 'audit-start-error',
    CSV_VALIDATION: 'csv-validation-failed',
    CONTROL_EVIDENCE: 'generate-control-evidence-error',
    EVIDENCE_GENERATION: 'evidence-generation-error',
    EVIDENCE_GENERATION_MISSING_PARAMS:
        'evidence-generation-missing-params-error',
    EVIDENCE_GENERATION_NO_URL: 'evidence-generation-no-url-error',
} as const;
