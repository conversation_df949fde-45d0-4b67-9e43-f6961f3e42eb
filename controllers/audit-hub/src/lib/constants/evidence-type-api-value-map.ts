import type { AuditHubEvidenceTypeEnum } from '@globals/api-sdk/types';

/**
 * Maps evidence type enum values to their corresponding API values.
 * This is used to convert human-readable evidence types to API-friendly values.
 */
export const EVIDENCE_TYPE_API_VALUE_MAP = {
    'Miscellaneous Evidence': 'MISCELLANEOUS_EVIDENCE',
    Policies: 'POLICIES',
    'Evidence Library': 'EVIDENCE_LIBRARY',
    Assets: 'ASSETS',
    Personnel: 'PERSONNEL',
    'Test Evidence': 'TEST_EVIDENCE',
    Vendors: 'VENDORS',
    'Board of Directors': 'BOARD_OF_DIRECTORS',
    'Company Info': 'COMPANY_INFO',
    Control: 'CONTROL',
    'Terms of Service URL': 'TERMS_OF_SERVICE_URL',
    'Privacy Policy URL': 'PRIVACY_POLICY_URL',
    'Employment Agreements': 'EMPLOYMENT_AGREEMENTS',
    'Job Description Document': 'JOB_DESCRIPTION_DOCUMENT',
    'Policy Acknowledgement': 'POLICY_ACKNOWLEDGEMENT',
} as const satisfies Record<AuditHubEvidenceTypeEnum, string>;

/**
 * Helper function to get the API value for a given evidence type.
 * Falls back to the original value if no mapping is found.
 */
export const getEvidenceTypeApiValue = (
    evidenceType: AuditHubEvidenceTypeEnum,
): string => {
    return EVIDENCE_TYPE_API_VALUE_MAP[evidenceType];
};
