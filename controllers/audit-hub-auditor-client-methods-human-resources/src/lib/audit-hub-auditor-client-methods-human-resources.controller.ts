import { isNil } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerDownloadAllHumanResourcesDocumentsOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class AuditHubAuditorClientMethodsHumanResourcesController {
    getDownloadAllHumanResourcesDocumentsQuery = new ObservedQuery(
        companiesControllerDownloadAllHumanResourcesDocumentsOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleHumanResourcesDownload() {
        if (
            isNil(sharedAuditHubController.auditByIdData?.framework.productId)
        ) {
            snackbarController.addSnackbar({
                id: 'download-all-human-resources-documents-error',
                props: {
                    title: t`Download Human Resources Documents Error`,
                    description: t`Failed to download human resources documents. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.getDownloadAllHumanResourcesDocumentsQuery.load({
            path: {
                xProductId:
                    sharedAuditHubController.auditByIdData.framework.productId,
            },
        });

        when(
            () => this.getDownloadAllHumanResourcesDocumentsQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'download-all-human-resources-documents-error',
                    props: {
                        title: t`Download Human Resources Documents Error`,
                        description: t`Failed to download human resources documents. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );

        when(
            () => !this.getDownloadAllHumanResourcesDocumentsQuery.isLoading,
            () => {
                if (!isNil(this.humanResourcesDownload?.signedUrl)) {
                    window.open(
                        this.humanResourcesDownload.signedUrl,
                        '_self',
                        'noopener, noreferrer',
                    );
                }
            },
        );
    }

    get humanResourcesDownload() {
        return this.getDownloadAllHumanResourcesDocumentsQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsHumanResourcesController =
    new AuditHubAuditorClientMethodsHumanResourcesController();
