import { isNil, isNumber } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import {
    evidenceLibraryControllerDownloadAllEvidenceOptions,
    evidenceLibraryControllerSendAllEvidenceByEmailMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

class AuditHubAuditorClientMethodsEvidenceLibraryController {
    sendAllEvidenceByEmailMutation = new ObservedMutation(
        evidenceLibraryControllerSendAllEvidenceByEmailMutation,
    );
    downloadAllEvidenceDirectlyQuery = new ObservedQuery(
        evidenceLibraryControllerDownloadAllEvidenceOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleSendAllEvidenceByEmail = (currentWorkspaceId: number) => {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedCustomerRequestsController.frameworkId),
            () => {
                this.sendAllEvidenceByEmailMutation.mutate({
                    path: {
                        auditFrameworkId:
                            sharedCustomerRequestsController.frameworkId,
                        xProductId: currentWorkspaceId,
                    },
                });

                when(
                    () => !this.sendAllEvidenceByEmailMutation.isPending,
                    () => {
                        if (this.sendAllEvidenceByEmailMutation.hasError) {
                            snackbarController.addSnackbar({
                                id: 'send-evidence-email-error',
                                props: {
                                    title: t`Failed to send evidence`,
                                    description: t`An error occurred while sending evidence by email. Please try again later.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        snackbarController.addSnackbar({
                            id: 'send-evidence-email-success',
                            props: {
                                title: t`Download requested`,
                                description: t`You will receive an email with download link shortly`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            },
        );
    };

    handleDownloadAllEvidenceDirectly() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedCustomerRequestsController.frameworkId),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.downloadAllEvidenceDirectlyQuery.load({
                    path: {
                        auditFrameworkId:
                            sharedCustomerRequestsController.frameworkId,
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                });

                // Handle errors when query is not loading and has an error
                when(
                    () => !this.downloadAllEvidenceDirectlyQuery.isLoading,
                    () => {
                        if (this.downloadAllEvidenceDirectlyQuery.error) {
                            snackbarController.addSnackbar({
                                id: 'download-all-evidence-directly-error',
                                props: {
                                    title: t`Download All Evidence Error`,
                                    description: t`Failed to download all evidence directly. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        }
                    },
                );
            },
        );
    }

    get allEvidenceDirectly() {
        return this.downloadAllEvidenceDirectlyQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsEvidenceLibraryController =
    new AuditHubAuditorClientMethodsEvidenceLibraryController();
