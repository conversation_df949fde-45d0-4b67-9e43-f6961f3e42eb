import { isEqual, isError, isNil, isObject } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { snackbarController } from '@controllers/snackbar';
import type {
    FetchDataResponseParams,
    GlobalFilterState,
} from '@cosmos/components/datatable';
import { accessReviewUserControllerGetApplicationUsersOptions } from '@globals/api-sdk/queries';
import type {
    ClientTypeEnum,
    ConnectionResponseDto,
    UserAccessReviewApplicationResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { buildQueryFromFilterParams } from './helpers/build-query-params.helper';

class AccessReviewsApplicationsUserController {
    applicationId: number | null = null;
    clientType: string | null = null;
    #filterValues: GlobalFilterState['filters'] = {};
    #queryCache = new Map<string, Record<string, unknown>>();

    accessReviewsApplicationsUser = new ObservedQuery(
        accessReviewUserControllerGetApplicationUsersOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Computed getters.
     */
    get accessReviewApplicationUsersList(): UserAccessReviewApplicationResponseDto[] {
        return this.accessReviewsApplicationsUser.data?.data ?? [];
    }

    get accessReviewApplicationUsersTotal(): number {
        return this.accessReviewsApplicationsUser.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.accessReviewsApplicationsUser.isLoading;
    }

    get accessReviewApplicationConnections(): ConnectionResponseDto[] {
        return sharedConnectionsController.allConfiguredConnectionsByClientType;
    }

    setApplicationId = (id: number | null): void => {
        this.applicationId = id;
    };

    setClientType = (clientType: string | null): void => {
        this.clientType = clientType;
    };

    updateFilterValues = (filters: GlobalFilterState['filters']): void => {
        const newFilterValues: GlobalFilterState['filters'] = {};

        Object.entries(filters).forEach(([key, filterData]) => {
            if (
                isObject(filterData) &&
                'value' in filterData &&
                !isNil(filterData.value)
            ) {
                newFilterValues[key] = {
                    value: filterData.value,
                    filterType: filterData.filterType,
                };
            }
        });

        if (!isEqual(newFilterValues, this.#filterValues)) {
            this.#filterValues = newFilterValues;
            // Clear query cache when filters change
            this.#queryCache.clear();
        }
    };

    /**
     * Builds a query object from FetchDataResponseParams
     * Memoizes results for better performance.
     */
    buildQueryFromParams = (
        params: FetchDataResponseParams,
    ): Record<string, unknown> => {
        // Include applicationId and clientType in cache key to ensure cache invalidation
        // when these values change
        const cacheKey = JSON.stringify({
            params,
            applicationId: this.applicationId,
            clientType: this.clientType,
        });

        if (this.#queryCache.has(cacheKey)) {
            const cachedQuery = this.#queryCache.get(cacheKey);

            if (cachedQuery) {
                return cachedQuery;
            }
        }

        const query = buildQueryFromFilterParams(
            params,
            this.applicationId,
            this.clientType,
        );

        // Cache the result
        this.#queryCache.set(cacheKey, query);

        return query;
    };

    loadAccessReviewApplicationUsers = (
        params: FetchDataResponseParams,
    ): void => {
        if (this.isLoading) {
            return;
        }

        const query = this.buildQueryFromParams(params);

        try {
            this.accessReviewsApplicationsUser.load({
                query: {
                    ...query,
                    clientType: this.clientType
                        ? (this.clientType as ClientTypeEnum)
                        : undefined,
                },
            });
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'access-review-application-users-error',
                props: {
                    title: 'Failed to load application users',
                    description: isError(error)
                        ? error.message
                        : 'An unknown error occurred',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
        }
    };

    loadAccessReviewApplicationUsersConnections = (): void => {
        if (this.isLoading) {
            when(
                () => !this.isLoading,
                () => {
                    this.loadConnectionsWhenReady();
                },
            );

            return;
        }

        this.loadConnectionsWhenReady();
    };

    loadConnectionsWhenReady = (): void => {
        if (this.clientType) {
            sharedConnectionsController.allConfiguredConnectionsByClientTypeQuery.load(
                {
                    query: {
                        type: this.clientType as ClientTypeEnum,
                    },
                },
            );
        }
    };

    invalidateAccessReviewApplicationUsers = () => {
        this.accessReviewsApplicationsUser.invalidate();
    };
}

export const activeAccessReviewsApplicationsUserController =
    new AccessReviewsApplicationsUserController();
