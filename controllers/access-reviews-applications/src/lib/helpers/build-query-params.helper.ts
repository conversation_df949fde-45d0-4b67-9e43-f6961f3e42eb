import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { formatApplicationUserFilters } from './format-filter-value.helper';

/**
 * Builds a query object from FetchDataResponseParams for application users.
 *
 * @param params - The fetch data parameters from the datatable.
 * @param applicationId - The current application ID.
 * @param clientType - The current client type.
 * @returns A query object ready to be sent to the API.
 */
export const buildQueryFromFilterParams = (
    params: FetchDataResponseParams,
    applicationId: number | null,
    clientType: string | null,
): Record<string, unknown> => {
    const { pagination, globalFilter, sorting } = params;
    const { page, pageSize } = pagination;
    const { search, filters } = globalFilter;

    // Process filters once
    const formattedFilters = formatApplicationUserFilters(filters);

    // Build query object
    const query: Record<string, unknown> = {
        clientType,
        applicationId,
        limit: pageSize,
        page,
    };

    // Only add q parameter if search is not empty
    if (search) {
        query.q = search;
    }

    // Add sorting if available
    if (!isEmpty(sorting)) {
        query.sort = sorting[0].id;
        query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
    }

    // Add array parameters
    if (
        Array.isArray(formattedFilters.employmentStatus) &&
        !isEmpty(formattedFilters.employmentStatus)
    ) {
        query['employmentStatus[]'] = formattedFilters.employmentStatus;
    }

    // Add simple parameters
    if (formattedFilters.warning) {
        query.warning = formattedFilters.warning;
    }
    if (formattedFilters.status) {
        query.status = formattedFilters.status;
    }
    if (formattedFilters.permission) {
        query.permission = formattedFilters.permission;
    }

    // Add comma-separated parameters as arrays
    if (formattedFilters.connections) {
        query['connections[]'] = formattedFilters.connections.split(',');
    }

    if (formattedFilters.groupIds) {
        query['groupIds[]'] = formattedFilters.groupIds.split(',');
    }

    return query;
};
