import { isNumber, isString } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import {
    saveBlob,
    sharedConnectionInfoQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { snackbarController } from '@controllers/snackbar';
import { userIdentitiesControllerGetUserVersionControlIdentitiesOptions } from '@globals/api-sdk/queries';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

const gitProviders = [
    'BITBUCKET',
    'GITHUB',
    'GITHUB_ENTERPRISE',
    'GITLAB',
    'GITLAB_ON_PREM',
    'AZURE_REPOS',
    'AWS_CODECOMMIT',
] as const;

type GitProvider = (typeof gitProviders)[number];

class AuditHubAuditorClientMethodsVersionControlController {
    getUserVersionControlIdentitiesQuery = new ObservedQuery(
        userIdentitiesControllerGetUserVersionControlIdentitiesOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadUserVersionControlIdentities() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !sharedConnectionInfoQueryController.isConnectionInfoLoading,
            () => {
                const clientType =
                    sharedConnectionInfoQueryController.connectionInfo
                        ?.versionControl[0].clientType;

                // Only load if clientType is a valid version control type
                // dto is not present on sdk, so we have to manually check
                if (
                    !clientType ||
                    !gitProviders.includes(clientType as GitProvider)
                ) {
                    return;
                }

                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }

                this.getUserVersionControlIdentitiesQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                    query: {
                        clientType: clientType as GitProvider,
                    },
                });
            },
        );

        when(
            () => !this.getUserVersionControlIdentitiesQuery.hasError,
            () => {
                if (this.getUserVersionControlIdentitiesQuery.error) {
                    snackbarController.addSnackbar({
                        id: 'load-user-version-control-identities-error',
                        props: {
                            title: t`Load User Version Control Identities Error`,
                            description: t`Failed to load user version control identities. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );

        when(
            () => !this.getUserVersionControlIdentitiesQuery.isLoading,
            () => {
                const reportName = t`Version-control-accounts`;

                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                const productName = sharedWorkspacesController.getWorkspaceById(
                    sharedAuditHubController.auditByIdData.framework.productId,
                )?.name;

                const reportNameWithWorkspace = `${productName}-${reportName}`;

                const fileName =
                    sharedEntitlementFlagController.isMultipleWorkspacesEnabled
                        ? reportNameWithWorkspace
                        : reportName;

                if (!isString(this.userVersionControlIdentities)) {
                    return;
                }
                saveBlob(this.userVersionControlIdentities, fileName);
            },
        );
    }

    get userVersionControlIdentities() {
        return this.getUserVersionControlIdentitiesQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsVersionControlController =
    new AuditHubAuditorClientMethodsVersionControlController();
