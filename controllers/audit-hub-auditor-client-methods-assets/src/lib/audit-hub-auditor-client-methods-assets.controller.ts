import { snackbarController } from '@controllers/snackbar';
import { assetsControllerGetAssetReportOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class AuditHubAuditorClientMethodsAssetsController {
    getAssetsReportQuery = new ObservedQuery(
        assetsControllerGetAssetReportOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleDownloadAssets() {
        this.getAssetsReportQuery.load();
        when(
            () => !this.getAssetsReportQuery.isLoading,
            () => {
                if (this.getAssetsReportQuery.error) {
                    snackbarController.addSnackbar({
                        id: 'download-assets-error',
                        props: {
                            title: t`Download Assets Error`,
                            description: t`Failed to download assets report. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    }

    get downloadsAssets() {
        return this.getAssetsReportQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsAssetsController =
    new AuditHubAuditorClientMethodsAssetsController();
