import { snackbarController } from '@controllers/snackbar';
import { connectionsControllerGetConnectionInfoOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class ConnectionInfoQueryController {
    getConnectionInfoQuery = new ObservedQuery(
        connectionsControllerGetConnectionInfoOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadConnectionInfo() {
        this.getConnectionInfoQuery.load({});

        when(
            () => this.getConnectionInfoQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-connection-info-error',
                    props: {
                        title: t`Connection Info Error`,
                        description: t`Failed to load connection information. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get connectionInfo() {
        return this.getConnectionInfoQuery.data;
    }

    get isConnectionInfoLoading() {
        return this.getConnectionInfoQuery.isLoading;
    }

    get connectionInfoHasError() {
        return this.getConnectionInfoQuery.hasError;
    }
}

export const sharedConnectionInfoQueryController =
    new ConnectionInfoQueryController();
