import { auditorControllerGetLatestAuditorFrameworkEvidencePingOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class AuditorFrameworkEvidenceQueryController {
    getLatestAuditorFrameworkEvidencePingQuery = new ObservedQuery(
        auditorControllerGetLatestAuditorFrameworkEvidencePingOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadLatestAuditorFrameworkEvidencePingQuery(auditId: string) {
        this.getLatestAuditorFrameworkEvidencePingQuery.load({
            path: { auditorFrameworkId: auditId },
        });
    }

    get latestAuditorFrameworkEvidencePingData() {
        return this.getLatestAuditorFrameworkEvidencePingQuery.data;
    }

    get isLatestAuditorFrameworkEvidencePingIsLoading() {
        return this.getLatestAuditorFrameworkEvidencePingQuery.isLoading;
    }

    get latestAuditorFrameworkEvidencePingHasError() {
        return this.getLatestAuditorFrameworkEvidencePingQuery.hasError;
    }

    get companyArchiveStatus() {
        return this.getLatestAuditorFrameworkEvidencePingQuery.data
            ?.companyArchiveStatus;
    }
}

export const sharedAuditorFrameworkEvidenceQueryController =
    new AuditorFrameworkEvidenceQueryController();
