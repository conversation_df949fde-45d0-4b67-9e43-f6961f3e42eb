import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetCompanySummaryOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class CompanySummaryQueryController {
    getCompanySummaryQuery = new ObservedQuery(
        companiesControllerGetCompanySummaryOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadCompanySummary() {
        this.getCompanySummaryQuery.load({});

        when(
            () =>
                !this.companySummaryIsLoading &&
                this.getCompanySummaryQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-company-summary-error',
                    props: {
                        title: t`Company Summary Error`,
                        description: t`Failed to load company summary. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get companySummary() {
        return this.getCompanySummaryQuery.data;
    }

    get companySummaryIsLoading() {
        return this.getCompanySummaryQuery.isLoading;
    }

    get companySummaryHasError() {
        return this.getCompanySummaryQuery.hasError;
    }
}

export const sharedCompanySummaryQueryController =
    new CompanySummaryQueryController();
