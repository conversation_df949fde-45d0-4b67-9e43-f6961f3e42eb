import { isNil, isNumber } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetLatestCompanyArchiveStatusOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class CompanyArchiveStatusQueryController {
    companiesControllerGetLatestCompanyArchiveStatusQuery = new ObservedQuery(
        companiesControllerGetLatestCompanyArchiveStatusOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadLatestCompanyArchiveStatus() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedAuditHubController.auditByIdData),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.companiesControllerGetLatestCompanyArchiveStatusQuery.load(
                    {
                        path: {
                            xProductId:
                                sharedAuditHubController.auditByIdData.framework
                                    .productId,
                        },
                        query: {
                            category: 'PRE_AUDIT',
                            auditFrameworkId:
                                sharedCustomerRequestsController.frameworkId,
                        },
                    },
                );
            },
        );

        when(
            () =>
                this.companiesControllerGetLatestCompanyArchiveStatusQuery
                    .hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-latest-company-archive-status-error',
                    props: {
                        title: t`Company Archive Status Error`,
                        description: t`Failed to load company archive status. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get latestCompanyArchiveStatusData() {
        return this.companiesControllerGetLatestCompanyArchiveStatusQuery.data;
    }

    get isLatestCompanyArchiveStatusLoading() {
        return this.companiesControllerGetLatestCompanyArchiveStatusQuery
            .isLoading;
    }

    get latestCompanyArchiveStatusHasError() {
        return this.companiesControllerGetLatestCompanyArchiveStatusQuery
            .hasError;
    }

    get companyArchiveStatusSuccess() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'SUCCESS'
        );
    }

    get companyArchiveStatusFailed() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'FAILED'
        );
    }

    get companyArchiveStatusPending() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'PENDING'
        );
    }
}

export const sharedCompanyArchiveStatusQueryController =
    new CompanyArchiveStatusQueryController();
