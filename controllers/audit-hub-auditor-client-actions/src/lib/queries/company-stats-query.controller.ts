import { isNumber } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetCompanyStatsForAuditorOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class CompanyStatsQueryController {
    getStatsQuery = new ObservedQuery(
        companiesControllerGetCompanyStatsForAuditorOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadStats() {
        when(
            () => !sharedAuditHubController.auditByIdIsLoading,
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.getStatsQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                });
            },
        );

        when(
            () => this.getStatsQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-stats-error',
                    props: {
                        title: t`Load Stats Error`,
                        description: t`Failed to load stats. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get stats() {
        return this.getStatsQuery.data;
    }

    get isStatsLoading() {
        return this.getStatsQuery.isLoading;
    }

    get statsHasError() {
        return this.getStatsQuery.hasError;
    }
}

export const sharedCompanyStatsQueryController =
    new CompanyStatsQueryController();
