import { isNumber } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { evidenceLibraryControllerGetTotalEvidenceByFrameworkIdOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class TotalEvidenceQueryController {
    getTotalEvidenceByFrameworkIdQuery = new ObservedQuery(
        evidenceLibraryControllerGetTotalEvidenceByFrameworkIdOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleGetTotalEvidenceByFrameworkId(auditId: string) {
        when(
            () => !sharedAuditHubController.auditByIdIsLoading,
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.getTotalEvidenceByFrameworkIdQuery.load({
                    path: {
                        auditorFrameworkId: auditId,
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                });
            },
        );

        when(
            () => this.getTotalEvidenceByFrameworkIdQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'get-total-evidence-by-framework-id-error',
                    props: {
                        title: t`Get Total Evidence Error`,
                        description: t`Failed to get total evidence by framework ID. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get totalEvidenceByFrameworkIdData() {
        return this.getTotalEvidenceByFrameworkIdQuery.data;
    }

    get totalEvidenceByFrameworkIdDataIsLoading() {
        return this.getTotalEvidenceByFrameworkIdQuery.isLoading;
    }

    get totalEvidenceByFrameworkIdHasError() {
        return this.getTotalEvidenceByFrameworkIdQuery.hasError;
    }
}

export const sharedTotalEvidenceQueryController =
    new TotalEvidenceQueryController();
