import { snackbarController } from '@controllers/snackbar';
import { vendorsControllerGetVendorReportOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class AuditHubAuditorClientMethodsVendorsController {
    getVendorReportQuery = new ObservedQuery(
        vendorsControllerGetVendorReportOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleDownloadVendors() {
        this.getVendorReportQuery.load({ query: { isArchived: true } });

        when(
            () => this.getVendorReportQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'download-vendors-error',
                    props: {
                        title: t`Download Vendors Error`,
                        description: t`Failed to download vendors report. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get vendorReportData() {
        return this.getVendorReportQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsVendorsController =
    new AuditHubAuditorClientMethodsVendorsController();
