import { isNil, isNumber, isString } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { saveBlob } from '@controllers/audit-hub-auditor-client-actions';
import { snackbarController } from '@controllers/snackbar';
import { connectionsControllerGetConnectionReportOptions } from '@globals/api-sdk/queries';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class AuditHubAuditorClientMethodsConnectionsController {
    downloadConnectionsReportQuery = new ObservedQuery(
        connectionsControllerGetConnectionReportOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    handleDownloadConnections() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedAuditHubController.auditByIdData),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.downloadConnectionsReportQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                });
            },
        );

        when(
            () => this.downloadConnectionsReportQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'download-connections-report-error',
                    props: {
                        title: t`Download Connections Report Error`,
                        description: t`Failed to download connections report. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );

        when(
            () => !this.downloadConnectionsReportQuery.isLoading,
            () => {
                const { isMultipleWorkspacesEnabled } =
                    sharedEntitlementFlagController;

                let filename = t`Connections-Report`;

                if (isMultipleWorkspacesEnabled) {
                    const productName =
                        sharedWorkspacesController.getWorkspaceById(
                            sharedAuditHubController.auditByIdData?.framework
                                .productId as number,
                        )?.name;

                    filename = `${productName}-${filename}`;
                }
                if (!isString(this.connectionsReport)) {
                    return;
                }
                saveBlob(this.connectionsReport, filename);
            },
        );
    }

    get connectionsReport() {
        return this.downloadConnectionsReportQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsConnectionsController =
    new AuditHubAuditorClientMethodsConnectionsController();
