import { isNumber, isString } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import {
    saveBlob,
    sharedConnectionInfoQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { snackbarController } from '@controllers/snackbar';
import { userIdentitiesControllerGetUserInfrastructureIdentitiesOptions } from '@globals/api-sdk/queries';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

type InfrastructureClientType =
    | 'AWS'
    | 'AWS_GOV_CLOUD'
    | 'AWS_ORG_UNITS'
    | 'GCP'
    | 'HEROKU'
    | 'AZURE'
    | 'AZURE_ORG_UNITS'
    | 'MONGO_DB_ATLAS'
    | 'CLOUDFLARE';

class AuditHubAuditorClientMethodsInfrastructureController {
    userIdentitiesControllerGetUserInfrastructureIdentitiesQuery =
        new ObservedQuery(
            userIdentitiesControllerGetUserInfrastructureIdentitiesOptions,
        );

    constructor() {
        makeAutoObservable(this);
    }

    loadUserIdentitiesControllerGetUserInfrastructureIdentities() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !sharedConnectionInfoQueryController.isConnectionInfoLoading,
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.userIdentitiesControllerGetUserInfrastructureIdentitiesQuery.load(
                    {
                        path: {
                            xProductId:
                                sharedAuditHubController.auditByIdData.framework
                                    .productId,
                        },
                        query: {
                            clientType: sharedConnectionInfoQueryController
                                .connectionInfo?.infrastructure[0]
                                .clientType as InfrastructureClientType,
                        },
                    },
                );
            },
        );

        when(
            () =>
                !this
                    .userIdentitiesControllerGetUserInfrastructureIdentitiesQuery
                    .isLoading,
            () => {
                const reportName = t`Infrastructure-accounts`;
                const productName = sharedWorkspacesController.getWorkspaceById(
                    sharedAuditHubController.auditByIdData?.framework
                        .productId as number,
                )?.name;

                const reportNameWithWorkspace = `${productName}-${reportName}`;

                const fileName =
                    sharedEntitlementFlagController.isMultipleWorkspacesEnabled
                        ? reportNameWithWorkspace
                        : reportName;

                if (!isString(this.getUserInfrastructureIdentities)) {
                    return;
                }
                saveBlob(
                    this.getUserInfrastructureIdentities as unknown as string,
                    fileName,
                );
            },
        );

        when(
            () =>
                this
                    .userIdentitiesControllerGetUserInfrastructureIdentitiesQuery
                    .hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-user-infrastructure-identities-error',
                    props: {
                        title: t`Load User Infrastructure Identities Error`,
                        description: t`Failed to load user infrastructure identities. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get getUserInfrastructureIdentities() {
        return this.userIdentitiesControllerGetUserInfrastructureIdentitiesQuery
            .data;
    }
}

export const sharedAuditHubAuditorClientMethodsInfrastructureController =
    new AuditHubAuditorClientMethodsInfrastructureController();
