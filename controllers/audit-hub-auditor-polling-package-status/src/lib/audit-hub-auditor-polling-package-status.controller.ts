import {
    POLLING_INTERVAL_IN_MS,
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedCompanyArchiveStatusQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { makeAutoObservable, when } from '@globals/mobx';
import { getIsArchiveExpired } from '@models/auditor-client-audit';

class AuditHubAuditorPollingPackageStatusController {
    pollingInterval: NodeJS.Timeout | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    handleStartGetCompanyStatusPolling() {
        if (this.pollingInterval) {
            // Clear any existing interval
            clearInterval(this.pollingInterval);
        }

        const { query } =
            sharedCompanyArchiveStatusQueryController.companiesControllerGetLatestCompanyArchiveStatusQuery;

        // Start polling with setInterval
        this.pollingInterval = setInterval(() => {
            if (!query) {
                return;
            }

            query.refetch();

            when(
                () => Boolean(query.state.data),
                () => {
                    if (query.state.data?.companyArchiveStatus !== 'SUCCESS') {
                        return;
                    }

                    if (
                        query.state.data.companyArchiveUpdatedAt &&
                        !getIsArchiveExpired(
                            new Date(query.state.data.companyArchiveUpdatedAt),
                        )
                    ) {
                        this.handleStopGetCompanyStatusPolling();
                        sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatus();
                    }
                },
            );
        }, POLLING_INTERVAL_IN_MS);
    }

    handleStopGetCompanyStatusPolling() {
        if (!this.pollingInterval) {
            return;
        }

        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
    }
}

export const sharedAuditHubAuditorPollingPackageStatusController =
    new AuditHubAuditorPollingPackageStatusController();
