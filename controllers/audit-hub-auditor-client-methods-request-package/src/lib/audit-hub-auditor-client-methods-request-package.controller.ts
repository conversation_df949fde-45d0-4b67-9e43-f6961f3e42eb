import { isNil, isNumber } from 'lodash-es';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedCompanyArchiveStatusQueryController } from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditHubAuditorPollingPackageStatusController } from '@controllers/audit-hub-auditor-polling-package-status';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetLatestCompanyArchiveOptions } from '@globals/api-sdk/queries';
import type { SignedUrlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class AuditHubAuditorClientMethodsRequestPackageController {
    getLatestCompanyArchiveQuery = new ObservedQuery(
        companiesControllerGetLatestCompanyArchiveOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    downloadPreAuditPackage(category: 'PRE_AUDIT' | 'CONTROL_EVIDENCE'): void {
        when(
            () =>
                !isNil(
                    sharedAuditHubController.auditByIdData?.framework.productId,
                ),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.getLatestCompanyArchiveQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                    query: {
                        category,
                        auditFrameworkId:
                            sharedCustomerRequestsController.frameworkId,
                    },
                });
                sharedAuditHubAuditorPollingPackageStatusController.handleStartGetCompanyStatusPolling();
            },
        );

        when(
            () => this.getLatestCompanyArchiveQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'latest-company-archive-query-error',
                    props: {
                        title: t`Company Archive Error`,
                        description: t`Failed to load company archive data. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );

        when(
            () => !this.getLatestCompanyArchiveQuery.isLoading,
            () => {
                if (isNil(this.preAuditPackage?.signedUrl)) {
                    return;
                }
                window.open(
                    this.preAuditPackage.signedUrl,
                    '_self',
                    'noopener, noreferrer',
                );
            },
        );
    }

    get preAuditPackage(): SignedUrlResponseDto | null {
        return this.getLatestCompanyArchiveQuery.data;
    }

    get downloadPreAuditPackageIsLoading(): boolean {
        return this.getLatestCompanyArchiveQuery.isLoading;
    }

    generatePreAuditPackage() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedCustomerRequestsController.frameworkId),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                sharedAuditHubAuditController.loadAllCompanies(
                    sharedAuditHubController.auditByIdData.framework.productId,
                );

                sharedAuditHubAuditorPollingPackageStatusController.handleStartGetCompanyStatusPolling();
                sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus();
            },
        );
    }
}

export const sharedAuditHubAuditorClientMethodsRequestPackageController =
    new AuditHubAuditorClientMethodsRequestPackageController();
