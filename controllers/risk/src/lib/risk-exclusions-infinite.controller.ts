import type { ListBoxItemData } from '@cosmos/components/list-box';
import { riskManagementControllerGetRisksWithExclusionsInfiniteOptions } from '@globals/api-sdk/queries';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

type RiskExclusionsSearchQuery = NonNullable<
    Required<
        Parameters<
            typeof riskManagementControllerGetRisksWithExclusionsInfiniteOptions
        >
    >[0]['query']
>;

export type RiskListBoxItemData = ListBoxItemData & {
    riskCode: string;
};

class RiskExclusionsInfiniteController {
    #lastSearchQuery = '';

    constructor() {
        makeAutoObservable(this);
    }

    #risksWithExclusionsInfiniteQuery = new ObservedInfiniteQuery(
        riskManagementControllerGetRisksWithExclusionsInfiniteOptions,
    );

    get risksWithExclusionsInfiniteList(): RiskWithCustomFieldsResponseDto[] {
        return (
            this.#risksWithExclusionsInfiniteQuery.data?.pages.flatMap(
                (i) => i?.data ?? [],
            ) ?? []
        );
    }

    loadRisksWithExclusions = (query?: RiskExclusionsSearchQuery): void => {
        this.#risksWithExclusionsInfiniteQuery.load({
            query: {
                q: this.#lastSearchQuery,
                ...query,
            },
        });
    };

    get isLoading(): boolean {
        return this.#risksWithExclusionsInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.#risksWithExclusionsInfiniteQuery.isFetching;
    }

    get hasError(): boolean {
        return this.#risksWithExclusionsInfiniteQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.#risksWithExclusionsInfiniteQuery.hasNextPage;
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            this.#risksWithExclusionsInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.loadRisksWithExclusions();

            return;
        }

        this.#risksWithExclusionsInfiniteQuery.nextPage();
    };

    get options(): RiskListBoxItemData[] {
        return this.risksWithExclusionsInfiniteList.map((risk) => {
            return {
                id: risk.id.toString(),
                label: risk.title,
                description: risk.riskId,
                value: risk.id.toString(),
                riskCode: risk.riskId,
            };
        });
    }
}

export const sharedRiskExclusionsInfiniteController =
    new RiskExclusionsInfiniteController();
