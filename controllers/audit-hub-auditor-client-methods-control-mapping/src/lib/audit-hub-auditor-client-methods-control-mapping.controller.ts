import { isNumber, isString } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { saveBlob } from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditorController } from '@controllers/auditor';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerDownloadControlsOptions } from '@globals/api-sdk/queries';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class AuditHubAuditorClientMethodsControlMappingController {
    grcControllerDownloadControlsOptionsQuery = new ObservedQuery(
        grcControllerDownloadControlsOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadGrcControllerDownloadControlsOptions() {
        when(
            () =>
                !sharedAuditorController.auditSummaryByIdIsLoading &&
                !sharedCurrentCompanyController.isLoading,
            () => {
                const slug =
                    sharedAuditorController.auditSummaryByIdData
                        ?.auditorFramework.auditorFrameworkType.relatedFramework
                        ?.slug;

                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.grcControllerDownloadControlsOptionsQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                    query: {
                        frameworkSlug: slug,
                    },
                });
            },
        );

        when(
            () => !this.grcControllerDownloadControlsOptionsQuery.isLoading,
            () => {
                let filename = t`Controls-to-Requirements`;

                const productName = sharedWorkspacesController.getWorkspaceById(
                    sharedAuditHubController.auditByIdData?.framework
                        .productId as number,
                )?.name;

                if (
                    sharedEntitlementFlagController.isMultipleWorkspacesEnabled
                ) {
                    filename = `${productName}-${filename}`;
                }
                if (!isString(this.grcControllerDownloadControlsData)) {
                    return;
                }
                saveBlob(this.grcControllerDownloadControlsData, filename);
            },
        );

        when(
            () => this.grcControllerDownloadControlsOptionsQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-grc-controller-download-controls-options-error',
                    props: {
                        title: t`Load GRC Controller Download Controls Options Error`,
                        description: t`Failed to load GRC controller download controls options. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    get grcControllerDownloadControlsData() {
        return this.grcControllerDownloadControlsOptionsQuery.data;
    }
}

export const sharedAuditHubAuditorClientMethodsControlMappingController =
    new AuditHubAuditorClientMethodsControlMappingController();
