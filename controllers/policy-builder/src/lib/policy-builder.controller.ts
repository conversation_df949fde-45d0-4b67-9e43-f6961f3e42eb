import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    policiesControllerGetPolicyAssociatedControlsOptions,
    policiesControllerGetPolicyAssociatedFrameworksOptions,
    policiesControllerGetPolicyOptions,
    policiesControllerUpdatePolicyMutation,
    policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions,
    policyVersionControllerGetPolicyVersionOptions,
    policyVersionControllerPostPolicyVersionMutation,
    policyVersionControllerPutPolicyVersionExplanationOfChangesMutation,
    policyVersionControllerPutPolicyVersionStatusMutation,
    policyVersionControllerUpdatePolicyHtmlMutation,
    policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
} from '@globals/api-sdk/queries';
import type {
    PolicyVersionResponseDto,
    PolicyWithReplaceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export type PolicyBuilderConfig = 'EDITOR' | 'VIEWER';
export type PolicyBuilderTab =
    | 'overview'
    | 'policy'
    | 'version-history'
    | 'workflows';

class PolicyBuilderController {
    /**
     * ===== OBSERVABLE STATE =====.
     */
    policyId: number | null = null;
    currentVersionId: number | null = null;
    builderConfig: PolicyBuilderConfig = 'EDITOR';
    isNewVersion = false;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * ===== QUERIES =====.
     */
    policyQuery = new ObservedQuery(policiesControllerGetPolicyOptions);
    policyVersionQuery = new ObservedQuery(
        policyVersionControllerGetPolicyVersionOptions,
    );
    policyVersionHistoryQuery = new ObservedQuery(
        policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions,
    );
    policyControlsAssociatedQuery = new ObservedQuery(
        policiesControllerGetPolicyAssociatedControlsOptions,
    );
    policyFrameworksAssociatedQuery = new ObservedQuery(
        policiesControllerGetPolicyAssociatedFrameworksOptions,
    );

    /**
     * ===== MUTATIONS =====.
     */
    createPolicyVersionMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
    );
    updatePolicyMutation = new ObservedMutation(
        policiesControllerUpdatePolicyMutation,
    );
    updatePolicyHtmlMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyHtmlMutation,
    );
    updatePolicyVersionStatusMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
    );
    updatePolicyVersionToAuthoredMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
    );
    updatePolicyVersionExplanationMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionExplanationOfChangesMutation,
    );

    /**
     * ===== COMPUTED PROPERTIES =====.
     */
    get isPolicyLoading(): boolean {
        return this.policyQuery.isLoading || this.policyVersionQuery.isLoading;
    }

    get policy(): PolicyWithReplaceResponseDto | null {
        return this.policyQuery.data;
    }

    get currentVersion(): PolicyVersionResponseDto | null {
        return this.policyVersionQuery.data;
    }

    get hasPolicy(): boolean {
        return !isNil(this.policy);
    }

    get hasCurrentVersion(): boolean {
        return !isNil(this.currentVersion);
    }

    get isViewerMode(): boolean {
        return this.builderConfig === 'VIEWER';
    }

    get policyStatus(): string {
        return this.policy?.policyStatus ?? '';
    }

    get isDraft(): boolean {
        return this.currentVersion?.policyVersionStatus === 'DRAFT';
    }

    get isApproved(): boolean {
        return this.currentVersion?.policyVersionStatus === 'APPROVED';
    }

    get isPublished(): boolean {
        return this.currentVersion?.policyVersionStatus === 'PUBLISHED';
    }

    get policyControlsAssociated() {
        return this.policyControlsAssociatedQuery.data?.controls ?? [];
    }

    get policyFrameworksAssociated() {
        return this.policyFrameworksAssociatedQuery.data?.frameworks ?? [];
    }

    /**
     * ===== ACTIONS =====.
     */
    loadPolicyBuilder = (policyId: number, versionId?: number): void => {
        const isNewPolicy = this.policyId !== policyId;
        const isNewVersion = versionId && this.currentVersionId !== versionId;
        const hasValidData = this.policy && !this.policyQuery.isLoading;

        // Skip if same policy and version are already loaded
        if (!isNewPolicy && !isNewVersion && hasValidData) {
            return;
        }

        // Update state
        this.policyId = policyId;
        this.currentVersionId = versionId ?? null;

        // Load policy data (only if new policy or no data)
        if (isNewPolicy || !hasValidData) {
            this.loadPolicy();
        }

        // Handle version loading
        if (versionId) {
            // Load specific version if provided and different from current
            if (this.currentVersionId !== versionId) {
                this.loadPolicyVersion(versionId);
            }
        } else if (isNewPolicy || !this.currentVersionId) {
            // For new policies or when no version is loaded, wait for policy data then load default version
            when(
                () => Boolean(this.policy && !this.policyQuery.isLoading),
                () => {
                    if (!this.policy) {
                        return;
                    }

                    // Try to load the latest policy version first, fallback to current published
                    const latestVersionId = this.policy.latestPolicyVersion?.id;
                    const currentPublishedVersionId =
                        this.policy.currentPublishedPolicyVersion?.id;

                    const versionToLoad =
                        latestVersionId || currentPublishedVersionId;

                    if (
                        versionToLoad &&
                        this.currentVersionId !== versionToLoad
                    ) {
                        this.currentVersionId = versionToLoad;
                        this.loadPolicyVersion(versionToLoad);
                    }
                },
            );
        }

        // Load policy version history (only if new policy)
        if (isNewPolicy) {
            this.loadPolicyVersionHistory();
        }
    };

    loadPolicy = (): void => {
        if (isNil(this.policyId)) {
            return;
        }

        // Load main policy data
        this.policyQuery.load({ path: { id: this.policyId } });

        // Load associated data (controls and frameworks)
        this.loadAssociatedData();
    };

    loadAssociatedData = (): void => {
        if (
            isNil(sharedWorkspacesController.currentWorkspace) ||
            isNil(this.policyId)
        ) {
            // If workspace is not available yet, wait for it
            when(
                () => !isNil(sharedWorkspacesController.currentWorkspace),
                () => {
                    this.loadAssociatedData(); // Retry when workspace is available
                },
            );

            return;
        }

        const loadParams = {
            path: {
                xProductId: sharedWorkspacesController.currentWorkspace.id,
                id: this.policyId,
            },
        };

        // Load controls associated with this policy (only if not already loading)
        if (!this.policyControlsAssociatedQuery.isLoading) {
            this.policyControlsAssociatedQuery.load(loadParams);
        }

        // Load frameworks associated with this policy (only if not already loading)
        if (!this.policyFrameworksAssociatedQuery.isLoading) {
            this.policyFrameworksAssociatedQuery.load(loadParams);
        }
    };

    /**
     * Force refresh all policy data - useful when data might be stale.
     */
    refreshPolicyData = (): void => {
        if (isNil(this.policyId)) {
            return;
        }

        // Invalidate all queries to force fresh data
        this.policyQuery.invalidate();
        this.policyControlsAssociatedQuery.invalidate();
        this.policyFrameworksAssociatedQuery.invalidate();
        this.policyVersionHistoryQuery.invalidate();

        if (this.currentVersionId) {
            this.policyVersionQuery.invalidate();
        }

        // Reload everything
        this.loadPolicy();
    };

    /**
     * Clear current policy data - useful when navigating away.
     */
    clearPolicyData = (): void => {
        this.policyId = null;
        this.currentVersionId = null;

        // Unload all queries to clear their state
        this.policyQuery.unload();
        this.policyVersionQuery.unload();
        this.policyControlsAssociatedQuery.unload();
        this.policyFrameworksAssociatedQuery.unload();
        this.policyVersionHistoryQuery.unload();
    };

    loadPolicyVersion = (versionId: number): void => {
        if (isNil(this.policyId)) {
            return;
        }

        this.policyVersionQuery.load({
            path: {
                policyId: this.policyId,
                policyVersionId: versionId,
            },
        });
    };

    loadPolicyVersionHistory = (): void => {
        if (isNil(this.policyId)) {
            return;
        }

        this.policyVersionHistoryQuery.load({
            path: { policyId: this.policyId },
            query: { page: 1, limit: 50 },
        });
    };

    setBuilderConfig = (config: PolicyBuilderConfig): void => {
        this.builderConfig = config;
    };

    /**
     * ===== MUTATION ACTIONS =====.
     */

    /**
     * Finalize a draft policy version by updating it to authored status.
     */
    finalizeDraft = async (versionId: number): Promise<void> => {
        try {
            await this.updatePolicyVersionToAuthoredMutation.mutateAsync({
                path: { id: versionId },
            });

            // Reload the current version to get updated data
            this.loadPolicyVersion(versionId);

            snackbarController.addSnackbar({
                id: 'policy-finalize-success',
                hasTimeout: true,
                props: {
                    title: t`Draft finalized successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch (error) {
            console.error('Finalize failed:', error);
            snackbarController.addSnackbar({
                id: 'policy-finalize-error',
                props: {
                    title: t`Failed to finalize draft`,
                    description: t`Please try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            throw error;
        }
    };

    /**
     * Publish a policy version.
     */
    publishPolicy = async (
        versionId: number,
        policyId: number,
    ): Promise<void> => {
        try {
            await this.updatePolicyVersionStatusMutation.mutateAsync({
                path: {
                    policyVersionId: versionId,
                    policyId,
                },
                body: { policyVersionStatus: 'PUBLISHED' },
            });

            // Reload the current version to get updated data
            this.loadPolicyVersion(versionId);

            snackbarController.addSnackbar({
                id: 'policy-publish-success',
                hasTimeout: true,
                props: {
                    title: t`Policy published successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch (error) {
            console.error('Publish failed:', error);
            snackbarController.addSnackbar({
                id: 'policy-publish-error',
                props: {
                    title: t`Failed to publish policy`,
                    description: t`Please try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            throw error;
        }
    };

    /**
     * Approve a policy version.
     */
    approvePolicy = async (
        versionId: number,
        policyId: number,
    ): Promise<void> => {
        try {
            await this.updatePolicyVersionStatusMutation.mutateAsync({
                path: {
                    policyVersionId: versionId,
                    policyId,
                },
                body: { policyVersionStatus: 'APPROVED' },
            });

            // Reload the current version to get updated data
            this.loadPolicyVersion(versionId);

            snackbarController.addSnackbar({
                id: 'policy-approve-success',
                hasTimeout: true,
                props: {
                    title: t`Policy approved successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch (error) {
            console.error('Approve failed:', error);
            snackbarController.addSnackbar({
                id: 'policy-approve-error',
                props: {
                    title: t`Failed to approve policy`,
                    description: t`Please try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            throw error;
        }
    };

    /**
     * Download policy as PDF.
     */
    downloadPolicy = (policyId: number, versionId: number): void => {
        const downloadUrl = `/policies/${policyId}/policy-version/${versionId}/pdf/download`;

        window.open(downloadUrl, '_blank', 'noopener,noreferrer');

        snackbarController.addSnackbar({
            id: 'policy-download-success',
            hasTimeout: true,
            props: {
                title: t`Policy download started`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    /**
     * Reset controller state.
     */
    reset = (): void => {
        this.policyId = null;
        this.currentVersionId = null;
        this.builderConfig = 'EDITOR';
        this.isNewVersion = false;
    };
}

export const sharedPolicyBuilderController = new PolicyBuilderController();
