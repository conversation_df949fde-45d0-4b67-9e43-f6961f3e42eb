import { isEmpty } from 'lodash-es';
import React from 'react';
import { Avatar } from '@cosmos/components/avatar';
import { usersControllerListUsersInfiniteOptions } from '@globals/api-sdk/queries';
import type {
    RoleEnum,
    UserResponseDto,
    UsersControllerListUsersData,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import type { UserListBoxItemData } from './types/user-list-box-item-data.type';
import type { UserListBoxItemDataWithEntryId } from './types/user-list-box-item-data-with-entry-id.type';

const userToListBoxItemDataAdapter = (
    user: UserResponseDto,
): UserListBoxItemData => {
    const fullName = getFullName(user.firstName, user.lastName);

    return {
        id: user.id.toString(),
        label: fullName,
        startSlot: React.createElement(Avatar, {
            fallbackText: getInitials(fullName),
            imgSrc: user.avatarUrl ?? undefined,
            imgAlt: fullName,
            size: 'sm',
        }),
        description: user.email,
        value: user.id.toString(),
        userData: user,
        avatar: {
            fallbackText: getInitials(fullName),
            imgSrc: user.avatarUrl ?? undefined,
            imgAlt: fullName,
        },
    };
};

export class UsersInfiniteController {
    #roles: RoleEnum[] = [];
    #lastSearchQuery = '';

    constructor() {
        makeAutoObservable(this);
    }

    usersListInfiniteQuery = new ObservedInfiniteQuery(
        usersControllerListUsersInfiniteOptions,
    );

    get hasNextPage(): boolean {
        return this.usersListInfiniteQuery.hasNextPage;
    }

    get usersList(): UserResponseDto[] {
        return (
            this.usersListInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get usersInfiniteListOptionsWithEntryId(): UserListBoxItemDataWithEntryId[] {
        return this.usersList.map(
            (value: UserResponseDto): UserListBoxItemDataWithEntryId => ({
                id: String(value.id),
                entryId: value.entryId,
                label: getFullName(value.firstName, value.lastName),
                value: String(value.id),
                description: value.email,
                avatar: {
                    fallbackText: getInitials(
                        `${value.firstName} ${value.lastName}`,
                    ),
                    imgSrc: value.avatarUrl ?? undefined,
                    imgAlt: getFullName(value.firstName, value.lastName),
                },
            }),
        );
    }

    get hasError(): boolean {
        return this.usersListInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.usersListInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.usersListInfiniteQuery.isFetching;
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            this.usersListInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.loadUsers({ q: search });

            return;
        }

        this.usersListInfiniteQuery.nextPage();
    };

    get options(): UserListBoxItemData[] {
        return this.usersList.map(userToListBoxItemDataAdapter);
    }

    loadUsers = ({
        q,
        roles,
        withAllUsers,
        excludeReadOnlyUsers,
    }: {
        q?: string;
        roles?: RoleEnum[];
        withAllUsers?: boolean;
        excludeReadOnlyUsers?: boolean;
    }): void => {
        const query: UsersControllerListUsersData['query'] = {
            page: 1,
        };

        if (roles) {
            this.#roles = roles;
        }

        if (withAllUsers) {
            query.withAllUsers = true;
        }

        if (!isEmpty(this.#roles)) {
            query['roles[]'] = this.#roles;
        }

        if (q) {
            query.q = q;
        }

        if (excludeReadOnlyUsers) {
            query.excludeReadOnlyUsers = excludeReadOnlyUsers;
        }

        this.usersListInfiniteQuery.load({
            query,
        });
    };

    onFetchUsers = ({
        search,
        increasePage,
        excludeReadOnlyUsers,
        roles,
        withAllUsers,
    }: {
        search?: string;
        increasePage?: boolean;
        excludeReadOnlyUsers?: boolean;
        roles?: RoleEnum[];
        withAllUsers?: boolean;
    } = {}): void => {
        if (increasePage) {
            this.loadNextPage({ search });

            return;
        }
        this.loadUsers({
            q: search?.trim(),
            excludeReadOnlyUsers,
            roles,
            withAllUsers,
        });
    };
}

export const sharedUsersInfiniteController = new UsersInfiniteController();
