import {
    auditHubControllerGetAuditorsByFrameworkOptions,
    auditorControllerGetAuditorFrameworkSampleDatesOptions,
    auditorControllerGetCurrentClientOptions,
} from '@globals/api-sdk/queries';
import type {
    AuditorClientResponseDto,
    AuditorResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';

export interface AuditorDataType {
    id: string;
    content: string;
    avatarUrl: string | null;
}
class AuditHubAuditorClientAuditController {
    auditDatesQuery = new ObservedQuery(
        auditorControllerGetAuditorFrameworkSampleDatesOptions,
    );

    auditorsQuery = new ObservedQuery(
        auditHubControllerGetAuditorsByFrameworkOptions,
    );

    auditorClientInfoQuery = new ObservedQuery(
        auditorControllerGetCurrentClientOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadAuditorDetails() {
        this.auditorClientInfoQuery.load();
    }

    loadAuditDates(auditId: string) {
        this.auditDatesQuery.load({
            path: { auditorFrameworkId: auditId },
        });
    }

    get auditDatesData(): string[] {
        return this.auditDatesQuery.data?.dates ?? [];
    }

    loadAuditors(auditId: string) {
        this.auditorsQuery.load({
            path: { auditId },
        });
    }

    get auditorDetails(): AuditorClientResponseDto | null {
        return this.auditorClientInfoQuery.data;
    }

    get auditors(): AuditorResponseDto[] {
        return this.auditorsQuery.data?.auditors ?? [];
    }

    get auditorsData(): AuditorDataType[] {
        return (
            this.auditorsQuery.data?.auditors.map((auditor) => ({
                id: String(auditor.id),
                content: getFullName(auditor.firstName, auditor.lastName),
                avatarUrl: auditor.avatarUrl ?? null,
            })) ?? []
        );
    }

    get auditorsIsLoading(): boolean {
        return this.auditorsQuery.isLoading;
    }
}

export const sharedAuditHubAuditorClientAuditController =
    new AuditHubAuditorClientAuditController();
