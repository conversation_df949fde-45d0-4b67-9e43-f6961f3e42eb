import type { Flatfile } from '@flatfile/api';

export const FlatfileEntityType = {
    RISK: 'RISK',
} as const;

export type FlatfileEntityType =
    (typeof FlatfileEntityType)[keyof typeof FlatfileEntityType];

export interface CreateSpaceResponse {
    space: {
        spaceId: string;
        accessToken: string;
        blueprint: Flatfile.SheetConfig;
        workbook: Flatfile.Workbook;
        space: Flatfile.Space;
    };
}

export interface CreateSpaceParams {
    entityType: FlatfileEntityType;
}

export interface BulkImportRisksParams {
    sheetId: string;
    spaceId: string;
    entityType: FlatfileEntityType;
}

export interface BulkImportRisksResponse {
    successCount: number;
    failureCount: number;
}
