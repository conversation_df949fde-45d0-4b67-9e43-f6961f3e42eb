import { isNil } from 'lodash-es';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { auditorControllerRefreshControlEvidencePackageMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class AuditHubAuditorRefreshPackageController {
    auditStarted = false;
    auditorRefreshControlEvidencePackageMutation = new ObservedMutation(
        auditorControllerRefreshControlEvidencePackageMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    refreshControlEvidencePackage = (auditId: string, productId: number) => {
        this.auditorRefreshControlEvidencePackageMutation
            .mutateAsync({
                path: { auditorFrameworkId: auditId, xProductId: productId },
            })
            .then(() => {
                // generateAllCompany
                if (
                    isNil(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                sharedAuditHubAuditController.loadAllCompanies(
                    sharedAuditHubController.auditByIdData.framework.productId,
                );
                const isDownloadOnlyAudit =
                    sharedAuditHubController.auditByIdData.framework
                        .auditType === 'DOWNLOAD_ONLY_AUDIT';

                if (isDownloadOnlyAudit) {
                    snackbarController.addSnackbar({
                        id: 'refresh-control-evidence-package-success',
                        props: {
                            title: t`You will be notified via email when your download is ready.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'refresh-control-evidence-package-success',
                        props: {
                            title: t`Auditors will be notified via email when your download is ready.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'refresh-control-evidence-package-error',
                    props: {
                        title: t`Refresh Control Evidence Package Error`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    get auditorRefreshControlEvidencePackageData() {
        return this.auditorRefreshControlEvidencePackageMutation.response;
    }

    get isAuditorRefreshControlEvidencePackageLoading(): boolean {
        return this.auditorRefreshControlEvidencePackageMutation.isPending;
    }
}

export const sharedAuditHubAuditorRefreshPackageController =
    new AuditHubAuditorRefreshPackageController();
