import { isEmpty, isObject } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';

/**
 * Extracts the user ID from the datatable filters.
 * Now handles single-select owner filter (not multi-select).
 *
 * @param filters - The filters object from the datatable.
 * @returns The extracted user ID or undefined if not present.
 */
export function extractUserIdFromFilters(
    filters: FetchDataResponseParams['globalFilter']['filters'],
): number | undefined {
    if (isEmpty(filters)) {
        return undefined;
    }

    if (isEmpty(filters.userId)) {
        return undefined;
    }

    // Check if filters.userId exists and has a
    if (isEmpty(filters.userId.value)) {
        return undefined;
    }

    const userIdFilter = filters.userId.value;

    // Handle single-select format: { id: string, value: string, ... }
    if (userIdFilter && isObject(userIdFilter) && 'value' in userIdFilter) {
        return userIdFilter.value ? Number(userIdFilter.value) : undefined;
    }

    // Fallback for legacy multi-select format (first item only)
    if (Array.isArray(userIdFilter) && !isEmpty(userIdFilter)) {
        const firstItem = userIdFilter[0];

        if (firstItem && isObject(firstItem) && 'id' in firstItem) {
            return firstItem.id ? Number(firstItem.id) : undefined;
        }
    }

    return undefined;
}
