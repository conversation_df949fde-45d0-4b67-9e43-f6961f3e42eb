import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { policyRenewal } from '../types/policies-controller.types';

/**
 * Extracts the policy renewal status from the datatable filters.
 *
 * @param filters - The filters object from the datatable.
 * @returns The extracted policy renewal status or undefined if not present.
 */
export function extractRenewalStatusFromFilters(
    filters: FetchDataResponseParams['globalFilter']['filters'],
): policyRenewal | undefined {
    if (isEmpty(filters)) {
        return undefined;
    }
    // Check if filters.policyRenewal exists and has a value
    if (isEmpty(filters.policyRenewal)) {
        return undefined;
    }

    return filters.policyRenewal.value as policyRenewal;
}
