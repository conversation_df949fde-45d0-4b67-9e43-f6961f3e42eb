import { head, isEmpty, isNil } from 'lodash-es';
import {
    type ConnectionProps,
    sharedConnectionsController,
} from '@controllers/connections';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItems } from '@cosmos/components/list-box';
import {
    groupControllerGetGroupsOptions,
    policiesControllerDownloadAllPoliciesOptions,
    policiesControllerGetExternalStatusOptions,
    policiesControllerGetPoliciesCountByApprovalStatusOptions,
    policiesControllerGetPolicyListOptions,
} from '@globals/api-sdk/queries';
import type {
    GroupResponseDto,
    PoliciesControllerGetPolicyListData,
    PolicyOverviewResponse,
    PolicyTableResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { extractUserIdFromFilters } from './helpers/extract-user-id-from-filters.helper.js';
import {
    formatOverviewFilters,
    getOverviewFilters,
} from './helpers/policies-controller.helper';
import type {
    policyApprovalStatus,
    policyOverviewMetrics,
    policyRenewal,
} from './types/policies-controller.types';

export class PoliciesStore {
    // ===== OBSERVABLE STATE =====
    /**
     * Core data queries - these are the source of truth.
     */
    activePolicies = new ObservedQuery(policiesControllerGetPolicyListOptions);
    archivedPolicies = new ObservedQuery(
        policiesControllerGetPolicyListOptions,
    );
    overview = new ObservedQuery(
        policiesControllerGetPoliciesCountByApprovalStatusOptions,
    );
    groups = new ObservedQuery(groupControllerGetGroupsOptions);
    downloadAllPolicies = new ObservedQuery(
        policiesControllerDownloadAllPoliciesOptions,
    );
    externalPolicyStatus = new ObservedQuery(
        policiesControllerGetExternalStatusOptions,
    );

    /**
     * UI state - tracks current user interactions.
     */
    currentParams: FetchDataResponseParams | null = null;
    overviewFilter: policyApprovalStatus | policyRenewal | undefined =
        undefined;

    constructor() {
        makeAutoObservable(this);
    }

    // ===== COMPUTED VALUES =====
    // These are derived from observable state and automatically update

    /**
     * Active Policies - main data for the active policies table.
     */
    get activePoliciesList(): PolicyTableResponseDto[] {
        return this.activePolicies.data?.data ?? [];
    }

    get activePoliciesTotal(): number {
        return this.activePolicies.data?.total ?? 0;
    }

    get isActivePoliciesLoading(): boolean {
        return this.activePolicies.isLoading;
    }

    /**
     * Archived Policies - data for the archived policies table.
     */
    get archivedPoliciesList(): PolicyTableResponseDto[] {
        return this.archivedPolicies.data?.data ?? [];
    }

    get archivedPoliciesTotal(): number {
        return this.archivedPolicies.data?.total ?? 0;
    }

    get isArchivedPoliciesLoading(): boolean {
        return this.archivedPolicies.isLoading;
    }

    /**
     * Overview - dashboard metrics and statistics.
     */
    get overviewData(): PolicyOverviewResponse | null {
        return this.overview.data;
    }

    get hasPublishedPolicies(): boolean {
        return (
            !isNil(this.overviewData?.published) &&
            this.overviewData.published > 0
        );
    }

    get isOverviewLoading(): boolean {
        return this.overview.isLoading;
    }

    /**
     * Connections - derived from external connections store.
     */
    get hasExternalPolicyConnection(): boolean {
        return !isEmpty(
            sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                'EXTERNAL_POLICY',
            ),
        );
    }

    get externalPolicyConnection(): ConnectionProps | null {
        return (
            head(
                sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                    'EXTERNAL_POLICY',
                ),
            ) ?? null
        );
    }

    get hasBambooHrConnection(): boolean {
        return this.externalPolicyConnection?.clientType === 'BAMBOO_HR';
    }

    /**
     * External policy status - derived from external policy status query.
     */
    get hasPoliciesOutdated(): boolean {
        return this.externalPolicyStatus.data?.hasOutdated ?? false;
    }

    get hasPoliciesDeleted(): boolean {
        return this.externalPolicyStatus.data?.hasUnacceptable ?? false;
    }

    get showExternalPolicyBanner(): boolean {
        return this.hasExternalPolicyConnection;
    }

    get isExternalPolicyStatusLoading(): boolean {
        return this.externalPolicyStatus.isLoading;
    }

    /**
     * Groups - user groups for policy assignment.
     */
    get groupsList(): ListBoxItems {
        return (this.groups.data?.data ?? []).map(
            (value: GroupResponseDto): ListBoxItems[0] => ({
                id: String(value.id),
                label: value.name,
                value: String(value.id),
            }),
        );
    }

    get isGroupsLoading(): boolean {
        return this.groups.isLoading;
    }

    /**
     * Download All Policies - loading state for bulk download.
     */
    get isDownloadingAllPolicies(): boolean {
        return this.downloadAllPolicies.isLoading;
    }

    // ===== ACTIONS =====
    // These are the only methods that can modify observable state

    loadActivePolicies = (params: FetchDataResponseParams): void => {
        this.currentParams = params;
        const { globalFilter, pagination, sorting } = params;
        const userId = extractUserIdFromFilters(globalFilter.filters);

        // Extract filters from the data table (renewal status, approval status)
        const tableFilters = getOverviewFilters(globalFilter.filters);

        // Combine overview filter (from clicking metrics) with table filters
        const allFilters = this.overviewFilter
            ? [this.overviewFilter, ...tableFilters]
            : tableFilters;

        const queryParams: PoliciesControllerGetPolicyListData['query'] = {
            page: pagination.pageIndex + 1,
            limit: pagination.pageSize,
            q: globalFilter.search,
            sort: (sorting[0]?.id ?? 'NAME') as
                | 'NAME'
                | 'SLA'
                | 'APPROVED_DATE'
                | 'USER'
                | 'CREATED'
                | 'HTML_LAST_UPDATED'
                | 'RENEWAL_DATE'
                | 'PUBLISHED_DATE',
            sortDir: sorting[0]?.desc ? 'DESC' : 'ASC',
            userId,
            policyStatuses: ['ACTIVE', 'UNACCEPTABLE', 'OUTDATED'],
            ...formatOverviewFilters('TABLE_FILTER', allFilters),
        };

        this.activePolicies.load({ query: queryParams });
    };

    loadArchivedPolicies = (params: FetchDataResponseParams): void => {
        this.currentParams = params;
        const { globalFilter, pagination, sorting } = params;
        const userId = extractUserIdFromFilters(globalFilter.filters);

        // Extract filters from the data table (renewal status, approval status)
        const tableFilters = getOverviewFilters(globalFilter.filters);

        // Combine overview filter (from clicking metrics) with table filters
        const allFilters = this.overviewFilter
            ? [this.overviewFilter, ...tableFilters]
            : tableFilters;

        const queryParams: PoliciesControllerGetPolicyListData['query'] = {
            page: pagination.pageIndex + 1,
            limit: pagination.pageSize,
            q: globalFilter.search,
            sort: (sorting[0]?.id ?? 'NAME') as
                | 'NAME'
                | 'SLA'
                | 'APPROVED_DATE'
                | 'USER'
                | 'CREATED'
                | 'HTML_LAST_UPDATED'
                | 'RENEWAL_DATE'
                | 'PUBLISHED_DATE',
            sortDir: sorting[0]?.desc ? 'ASC' : 'DESC',
            userId,
            policyStatuses: ['ARCHIVED', 'REPLACED'],
            ...formatOverviewFilters('TABLE_FILTER', allFilters),
        };

        this.archivedPolicies.load({ query: queryParams });
    };

    loadOverview = (): void => {
        this.overview.load();
    };

    loadExternalPolicyStatus = (): void => {
        this.externalPolicyStatus.load();
    };

    setOverviewFilter = (filter: policyOverviewMetrics): void => {
        const filterMap: Record<
            policyOverviewMetrics,
            policyApprovalStatus | policyRenewal
        > = {
            renewalUpcoming: 'EXPIRE_SOON',
            renewalPastDue: 'EXPIRED',
            approvalNeedsApproval: 'NEEDS_APPROVAL',
            approvalReadyToPublish: 'APPROVED',
        };

        this.overviewFilter = filterMap[filter];

        if (this.currentParams) {
            this.loadActivePolicies(this.currentParams);
        }
    };

    clearOverviewFilter = (): void => {
        this.overviewFilter = undefined;

        if (this.currentParams) {
            this.loadActivePolicies(this.currentParams);
        }
    };

    loadGroups(params?: Parameters<typeof this.groups.load>[0]): void {
        if (isNil(params)) {
            this.groups.load({
                query: {
                    page: 1,
                    limit: 50,
                    includeIds: [],
                },
            });
        } else {
            this.groups.load(params);
        }
    }

    downloadAllPoliciesFile = (): void => {
        try {
            const downloadParams = {
                withAppendix: true,
            };

            // Trigger the async download request
            // The download will be handled via socket events and notifications
            this.downloadAllPolicies.load({ query: downloadParams });

            // Show success message to user
            snackbarController.addSnackbar({
                id: 'policies-download-initiated',
                props: {
                    title: t({
                        message: 'Download initiated',
                        comment: 'Success message when policy download starts',
                    }),
                    description: t({
                        message:
                            'Your download will begin shortly. You will be notified when it is ready.',
                        comment: 'Description for policy download initiation',
                    }),
                    severity: 'success',
                    closeButtonAriaLabel: t({
                        message: 'Close',
                        comment: 'Close button label',
                    }),
                },
            });
        } catch (error) {
            logger.error({
                message: 'Failed to initiate download of all policies',
                additionalInfo: { error },
            });

            snackbarController.addSnackbar({
                id: 'policies-download-error',
                props: {
                    title: t({
                        message: 'Download failed',
                        comment: 'Error message when policy download fails',
                    }),
                    description: t({
                        message:
                            'Unable to start the download. Please try again.',
                        comment: 'Description for policy download failure',
                    }),
                    severity: 'critical',
                    closeButtonAriaLabel: t({
                        message: 'Close',
                        comment: 'Close button label',
                    }),
                },
            });
        }
    };
}

export const sharedPoliciesController = new PoliciesStore();
