import { isEmpty, size } from 'lodash-es';
import { REVIEW_APPROVAL_STATUS } from '@components/controls';
import { snackbarController } from '@controllers/snackbar';
import {
    controlApprovalsControllerBulkUpsertControlApprovalsMutation,
    grcControllerArchiveControlsMutation,
    grcControllerBulkDeleteControlOwnersMutation,
    grcControllerBulkPutControlOwnersMutation,
    grcControllerUnArchiveControlsMutation,
} from '@globals/api-sdk/queries';
import type { BulkUpsertApprovalsRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlsController } from './controls.controller';
import { sharedControlsOwnersIntersectionController } from './controls-owners-intersection.controller';

interface ControlsBulkProps {
    isAllRowsSelected?: boolean;
    onSuccess?: () => void;
}

class ControlsBulkMutationController {
    archiveControlsMutation = new ObservedMutation(
        grcControllerArchiveControlsMutation,
    );

    unarchiveControlsMutation = new ObservedMutation(
        grcControllerUnArchiveControlsMutation,
    );

    bulkAddControlsOwnersMutation = new ObservedMutation(
        grcControllerBulkPutControlOwnersMutation,
    );

    bulkRemoveControlsOwnersMutation = new ObservedMutation(
        grcControllerBulkDeleteControlOwnersMutation,
    );

    bulkAddControlsApprovalsMutation = new ObservedMutation(
        controlApprovalsControllerBulkUpsertControlApprovalsMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isArchiving(): boolean {
        return this.archiveControlsMutation.isPending;
    }

    get isUnArchiving(): boolean {
        return this.unarchiveControlsMutation.isPending;
    }

    get isAddingApprovals(): boolean {
        return this.bulkAddControlsApprovalsMutation.isPending;
    }

    get hasError(): boolean {
        return (
            this.archiveControlsMutation.hasError ||
            this.unarchiveControlsMutation.hasError
        );
    }

    get isAssigningOwners(): boolean {
        return (
            this.bulkAddControlsOwnersMutation.isPending ||
            this.bulkRemoveControlsOwnersMutation.isPending
        );
    }

    markOutOfScopeControls = (
        controlIds: number[],
        bulkProps?: ControlsBulkProps,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { isAllRowsSelected = false, onSuccess } = bulkProps ?? {};

        if (!currentWorkspace) {
            return;
        }

        this.archiveControlsMutation.mutate({
            body: {
                controlIds,
                selectAll: isAllRowsSelected,
                workspaceId: currentWorkspace.id,
                rationale: 'Controls marked out of scope via bulk action',
            },
        });

        when(
            () => !this.isArchiving,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'archive-controls-error',
                        props: {
                            title: t`Error marking controls out of scope`,
                            description: t`An error occurred while marking controls out of scope. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                const controlCount = isAllRowsSelected
                    ? t`All`
                    : size(controlIds);

                sharedControlsController.invalidateControlsList();
                onSuccess?.();

                snackbarController.addSnackbar({
                    id: 'archive-controls-success',
                    props: {
                        title: t`Controls marked out of scope`,
                        description: t`${controlCount} controls were marked out of scope`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    markInScopeControls = (
        controlIds: number[],
        bulkProps?: ControlsBulkProps,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { isAllRowsSelected = false, onSuccess } = bulkProps ?? {};

        if (!currentWorkspace) {
            return;
        }

        this.unarchiveControlsMutation.mutate({
            body: {
                controlIds,
                selectAll: isAllRowsSelected,
                workspaceId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isUnArchiving,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'unarchive-controls-error',
                        props: {
                            title: t`Error marking controls in scope`,
                            description: t`An error occurred while marking controls out of scope. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                const controlCount = isAllRowsSelected
                    ? t`All`
                    : size(controlIds);

                sharedControlsController.invalidateControlsList();
                onSuccess?.();

                snackbarController.addSnackbar({
                    id: 'unarchive-controls-success',
                    props: {
                        title: t`Controls marked in scope`,
                        description: t`${controlCount} controls were marked in scope`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    bulkAddControlsOwners = (
        controlIds: number[],
        ownerIds: number[],
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.bulkAddControlsOwnersMutation.mutate({
            body: {
                controlIds,
                ownerIds,
                workspaceId: currentWorkspace.id,
            },
        });

        when(
            () => !this.bulkAddControlsOwnersMutation.isPending,
            () => {
                if (this.bulkAddControlsOwnersMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-assign-controls-owners-error',
                        props: {
                            title: t`Error assigning control owners`,
                            description: t`An error occurred while assigning control owners. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                const controlCount = size(controlIds);

                sharedControlsController.invalidateControlsList();

                snackbarController.addSnackbar({
                    id: 'bulk-assign-controls-owners-success',
                    props: {
                        title: t`Controls owners assigned`,
                        description: t`${controlCount} controls were assigned an owner`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    bulkRemoveControlsOwners = (
        controlIds: number[],
        ownerIds: number[],
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.bulkRemoveControlsOwnersMutation.mutate({
            body: {
                controlIds,
                ownerIds,
                workspaceId: currentWorkspace.id,
            },
        });

        when(
            () => !this.bulkRemoveControlsOwnersMutation.isPending,
            () => {
                if (this.bulkRemoveControlsOwnersMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-remove-controls-owners-error',
                        props: {
                            title: t`Error removing control owners`,
                            description: t`An error occurred while removing control owners. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                const controlCount = size(controlIds);

                sharedControlsController.invalidateControlsList();

                snackbarController.addSnackbar({
                    id: 'bulk-remove-controls-owners-success',
                    props: {
                        title: t`Controls owners removed`,
                        description: t`Owners removed from ${controlCount} controls`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    manageBulkControlOwners = (
        controlsIds: number[],
        newOwners: number[],
        initialOwners: number[],
    ): void => {
        if (
            isEmpty(controlsIds) &&
            isEmpty(newOwners) &&
            isEmpty(initialOwners)
        ) {
            return;
        }

        const ownersToAdd = newOwners.filter(
            (owner) => !initialOwners.includes(owner),
        );

        const ownersToRemove = initialOwners.filter(
            (owner) => !newOwners.includes(owner),
        );

        if (isEmpty(ownersToAdd) && isEmpty(ownersToRemove)) {
            return;
        }

        if (!isEmpty(ownersToAdd)) {
            this.bulkAddControlsOwners(controlsIds, ownersToAdd);
        }

        if (!isEmpty(ownersToRemove)) {
            this.bulkRemoveControlsOwners(controlsIds, ownersToRemove);
        }

        sharedControlsOwnersIntersectionController.removeQuery();
    };

    bulkAddControlsApprovals = (
        requestDto: BulkUpsertApprovalsRequestDto,
    ): void => {
        this.bulkAddControlsApprovalsMutation.mutate({
            body: {
                ...requestDto,
                status: REVIEW_APPROVAL_STATUS.PREPARE_FOR_APPROVERS,
            },
        });

        when(
            () => !this.isAddingApprovals,
            () => {
                if (this.bulkAddControlsApprovalsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-add-controls-approvals-error',
                        props: {
                            title: t`Error adding control approvals`,
                            description: t`An error occurred while adding control approvals. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                sharedControlsController.invalidateControlsList();
                const controlCount = size(requestDto.controlIds);

                snackbarController.addSnackbar({
                    id: 'bulk-add-controls-approvals-success',
                    props: {
                        title: t`Control approvals added`,
                        description: t`Approvals were added to ${controlCount} controls`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlsBulkMutationController =
    new ControlsBulkMutationController();
