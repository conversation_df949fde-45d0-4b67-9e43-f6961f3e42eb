import { isEmpty, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    grcControllerBulkPutControlRisksMutation,
    riskManagementControllerUpdateRiskPartiallyMutation,
} from '@globals/api-sdk/queries';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedControlRisksController } from './control-risks.controller';

class ControlRisksMutationController {
    updateRiskMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskPartiallyMutation,
    );

    associateRiskMutation = new ObservedMutation(
        grcControllerBulkPutControlRisksMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isUnmappingPending(): boolean {
        return this.updateRiskMutation.isPending;
    }

    get isMappingPending(): boolean {
        return this.associateRiskMutation.isPending;
    }

    get hasError(): boolean {
        return this.updateRiskMutation.hasError;
    }

    unMapRiskToControl = (controlId: number, risk: RiskResponseDto) => {
        risk.controls = risk.controls.filter(
            (control) => control.id !== controlId,
        );

        this.updateRiskMutation.mutate({
            path: { risk_id: risk.riskId },
            body: {
                title: risk.title,
                description: risk.description,
                treatmentPlan: risk.treatmentPlan,
                controls: risk.controls,
            },
        });

        when(
            () => !this.isUnmappingPending,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: `unmap-risk-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to unmap risk`,
                            description: t`An error occurred while unmapping the risk. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                sharedControlRisksController.invalidate();

                snackbarController.addSnackbar({
                    id: `unmap-risk-success-${uniqueId()}`,
                    props: {
                        title: t`Risk unmapped`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    mapRisksToControl = (controlId: number, risksCodes: string[]) => {
        if (isEmpty(risksCodes)) {
            return;
        }

        this.associateRiskMutation.mutate({
            path: { controlId },
            body: { riskIds: risksCodes },
        });

        when(
            () => !this.isMappingPending,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: `map-risk-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to map risk`,
                            description: t`An error occurred while mapping the risk. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                sharedControlRisksController.invalidate();

                snackbarController.addSnackbar({
                    id: `map-risk-success-${uniqueId()}`,
                    props: {
                        title: t`Risk mapped to control`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlRisksMutationController =
    new ControlRisksMutationController();
