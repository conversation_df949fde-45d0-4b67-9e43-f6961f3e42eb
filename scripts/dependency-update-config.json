{"criticalPackages": ["react", "react-dom", "@types/react", "@types/react-dom", "typescript", "vite", "@remix-run/dev", "@remix-run/node", "@remix-run/react", "@remix-run/serve", "mobx", "mobx-react-lite", "styled-components", "@types/styled-components", "eslint", "@typescript-eslint/eslint-plugin", "@typescript-eslint/parser"], "pinnedVersions": {"packages": {"@biomejs/biome": {"version": "2.0.5", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "@eslint/js": {"version": "9.29.0", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "@playwright/test": {"version": "1.53.1", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "@typescript-eslint/utils": {"version": "8.35.0", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "eslint-config-sheriff": {"version": "28.0.0", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "eslint-import-resolver-typescript": {"version": "4.4.3", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "eslint-plugin-storybook": {"version": "9.0.12", "reason": "Auto-pinned after test failure - testing/build tool update may have broken test pipeline"}, "typescript-eslint": {"version": "8.35.0", "reason": "Auto-pinned - converted from legacy format (likely auto-pinned in previous runs)"}, "react-hook-form": {"version": "7.59.0", "reason": "Auto-pinned after test failure - one of 2 updated packages (7.59.0 → 7.60.0)"}, "zod": {"version": "3.25.67", "reason": "Auto-pinned after test failure - one of 2 updated packages (3.25.67 → 3.25.75)"}}, "description": "Packages pinned to specific versions due to breaking changes or compatibility issues. Each package can be either a string version or an object with 'version' and 'reason' properties."}, "versionConstraints": {"packages": {}, "description": "Maximum allowed versions for packages with known issues. Example: {\"eslint\": \"8.57.0\", \"typescript\": \"5.3.3\"}"}, "autoPinning": {"enabled": true, "maxRetries": 2, "logAutoPins": true, "pinToLastWorkingVersion": true, "description": "Automatically pin packages that cause test failures and retry"}, "testCommands": {"required": [{"name": "Type Check", "command": "pnpm run typecheck", "description": "TypeScript compilation check"}, {"name": "<PERSON><PERSON>", "command": "pnpm run lint", "description": "ESLint validation"}, {"name": "Unit Tests", "command": "pnpm run test --run", "description": "Unit test execution"}], "optional": [{"name": "Build Tokens", "command": "pnpm run tokens", "description": "Design system tokens build"}]}, "updateLimits": {"maxUpdatesPerRun": 20, "description": "Maximum number of packages to update in a single run"}, "backupSettings": {"retentionDays": 7, "description": "Number of days to retain backup files"}}