#!/usr/bin/env node

/* eslint-disable no-console -- development script only */

/**
 * Cleanup Dependency Backups Script.
 *
 * Removes old package.json backup files created by the dependency update MVP script.
 * Configurable retention period with dry-run support for safety.
 */

import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { isEmpty, isError } from 'lodash-es';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// ANSI color codes for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
};

// Utility functions for colored output
const log = {
    /**
     * Log an info message.
     *
     * @param {string} msg - The message to log.
     */
    info: (msg) => {
        console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`);
    },
    /**
     * Log a success message.
     *
     * @param {string} msg - The message to log.
     */
    success: (msg) => {
        console.log(`${colors.green}✅ ${msg}${colors.reset}`);
    },
    /**
     * Log a warning message.
     *
     * @param {string} msg - The message to log.
     */
    warning: (msg) => {
        console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`);
    },
    /**
     * Log an error message.
     *
     * @param {string} msg - The message to log.
     */
    error: (msg) => {
        console.log(`${colors.red}❌ ${msg}${colors.reset}`);
    },
    /**
     * Log a header message.
     *
     * @param {string} msg - The message to log.
     */
    header: (msg) => {
        console.log(`\n${colors.cyan}${'='.repeat(60)}`);
        console.log(`${colors.cyan}${msg}`);
        console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    },
};

/**
 * Parse command line arguments.
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        days: 7,
        dryRun: false,
        help: false,
    };

    for (const arg of args) {
        if (arg.startsWith('--days=')) {
            const value = parseInt(arg.split('=')[1], 10);

            if (isNaN(value) || value < 1 || value > 365) {
                log.error('--days must be a number between 1 and 365');
                process.exit(1);
            }
            options.days = value;
        } else if (arg === '--dry-run') {
            options.dryRun = true;
        } else if (arg === '--help' || arg === '-h') {
            options.help = true;
        }
    }

    return options;
}

/**
 * Show help message.
 */
function showHelp() {
    console.log(`
${colors.cyan}Cleanup Dependency Backups Script${colors.reset}

${colors.bright}Usage:${colors.reset}
  node scripts/cleanup-dependency-backups.mjs [options]

${colors.bright}Options:${colors.reset}
  --days=N        Days to retain backups (default: 7)
  --dry-run       Preview what would be deleted
  --help, -h      Show this help message

${colors.bright}Examples:${colors.reset}
  node scripts/cleanup-dependency-backups.mjs --days=14
  node scripts/cleanup-dependency-backups.mjs --dry-run
  node scripts/cleanup-dependency-backups.mjs --days=3 --dry-run
`);
}

/**
 * Find all backup files.
 */
function findBackupFiles() {
    const backupFiles = [];

    try {
        const files = fs.readdirSync(projectRoot);

        for (const file of files) {
            if (file.startsWith('package.json.backup.')) {
                const filePath = path.join(projectRoot, file);
                const stats = fs.statSync(filePath);

                backupFiles.push({
                    name: file,
                    path: filePath,
                    created: stats.birthtime,
                    size: stats.size,
                });
            }
        }
    } catch (error) {
        log.error(
            `Failed to read directory: ${isError(error) ? error.message : String(error)}`,
        );
        process.exit(1);
    }

    return backupFiles.sort(
        (a, b) => b.created.getTime() - a.created.getTime(),
    );
}

/**
 * Format file size in human readable format.
 *
 * @param {number} bytes - The file size in bytes.
 * @returns {string} Human readable file size.
 */
function formatFileSize(bytes) {
    if (bytes === 0) {
        return '0 B';
    }

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

/**
 * Format date for display.
 *
 * @param {Date} date - The date to format.
 * @returns {string} Formatted date string.
 */
function formatDate(date) {
    return date.toISOString().replace('T', ' ').slice(0, 19);
}

/**
 * Main cleanup function.
 */
function cleanup() {
    const options = parseArgs();

    if (options.help) {
        showHelp();

        return;
    }

    log.header('Cleanup Dependency Backups');

    const backupFiles = findBackupFiles();

    if (isEmpty(backupFiles)) {
        log.info('No backup files found');

        return;
    }

    log.info(`Found ${backupFiles.length} backup file(s)`);

    // Calculate cutoff date
    const cutoffDate = new Date();

    cutoffDate.setDate(cutoffDate.getDate() - options.days);

    log.info(
        `Retention period: ${options.days} days (cutoff: ${formatDate(cutoffDate)})`,
    );

    // Find files to delete
    const filesToDelete = backupFiles.filter(
        (file) => file.created < cutoffDate,
    );
    const filesToKeep = backupFiles.filter(
        (file) => file.created >= cutoffDate,
    );

    if (isEmpty(filesToDelete)) {
        log.success('No old backup files to clean up');

        if (!isEmpty(filesToKeep)) {
            log.info(`Keeping ${filesToKeep.length} recent backup file(s):`);
            for (const file of filesToKeep) {
                log.info(
                    `  ${file.name} (${formatDate(file.created)}, ${formatFileSize(file.size)})`,
                );
            }
        }

        return;
    }

    // Show what will be deleted
    log.info(
        `${options.dryRun ? 'Would delete' : 'Deleting'} ${filesToDelete.length} old backup file(s):`,
    );

    let totalSize = 0;

    for (const file of filesToDelete) {
        totalSize = totalSize + file.size;
        log.info(
            `  ${file.name} (${formatDate(file.created)}, ${formatFileSize(file.size)})`,
        );
    }

    log.info(`Total size to be freed: ${formatFileSize(totalSize)}`);

    if (options.dryRun) {
        log.warning('Dry run mode - no files were actually deleted');

        return;
    }

    // Actually delete the files
    let deletedCount = 0;
    let deletedSize = 0;

    for (const file of filesToDelete) {
        try {
            fs.unlinkSync(file.path);
            deletedCount = deletedCount + 1;
            deletedSize = deletedSize + file.size;
        } catch (error) {
            log.error(
                `Failed to delete ${file.name}: ${isError(error) ? error.message : String(error)}`,
            );
        }
    }

    if (deletedCount > 0) {
        log.success(
            `Successfully deleted ${deletedCount} backup file(s), freed ${formatFileSize(deletedSize)}`,
        );
    }

    if (!isEmpty(filesToKeep)) {
        log.info(`Keeping ${filesToKeep.length} recent backup file(s)`);
    }
}

// Run the script
cleanup();
